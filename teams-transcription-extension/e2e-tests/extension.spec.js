/**
 * E2E 测试文件 - Teams Meeting Transcriber Extension
 * 使用 Playwright 测试扩展的核心功能和UI交互
 */

import { test, expect, chromium } from '@playwright/test';
import path from 'path';

// 扩展路径配置
const extensionPath = path.resolve('.');

test.describe('Teams Meeting Transcriber Extension E2E Tests', () => {
  let browser;
  let context;
  let page;

  // 每个测试前的设置
  test.beforeEach(async () => {
    // 启动带有扩展的Chrome浏览器
    browser = await chromium.launch({
      headless: false, // 设为true可以无头模式运行
      args: [
        `--disable-extensions-except=${extensionPath}`,
        `--load-extension=${extensionPath}`,
        '--disable-web-security',
        '--disable-features=VizDisplayCompositor',
        '--no-sandbox'
      ]
    });

    // 创建浏览器上下文
    context = await browser.newContext({
      permissions: ['microphone', 'camera']
    });

    // 创建新页面
    page = await context.newPage();

    // 设置页面事件监听
    page.on('console', msg => {
      if (msg.type() === 'error') {
        console.error('Page error:', msg.text());
      }
    });

    page.on('pageerror', error => {
      console.error('Page crash:', error);
    });
  });

  // 每个测试后的清理
  test.afterEach(async () => {
    if (page) await page.close();
    if (context) await context.close();
    if (browser) await browser.close();
  });

  test('应该成功加载扩展并注入UI面板', async () => {
    // 导航到测试页面
    await page.goto('/test-page.html');
    
    // 等待页面完全加载
    await page.waitForLoadState('networkidle');
    
    // 等待扩展初始化（给扩展一些时间来注入UI）
    await page.waitForTimeout(3000);
    
    // 验证扩展的UI面板是否被注入
    const transcriptionPanel = await page.locator('#teams-transcription-panel');
    await expect(transcriptionPanel).toBeVisible({ timeout: 10000 });
    
    // 验证面板的基本结构
    const header = transcriptionPanel.locator('.transcription-header');
    await expect(header).toBeVisible();
    
    const content = transcriptionPanel.locator('.transcription-content');
    await expect(content).toBeVisible();
    
    // 验证面板标题
    const title = header.locator('h3');
    await expect(title).toContainText('Teams Transcription');
  });

  test('应该正确响应开始转录按钮点击', async () => {
    await page.goto('/test-page.html');
    await page.waitForLoadState('networkidle');
    await page.waitForTimeout(3000);

    // 确保面板存在
    const transcriptionPanel = page.locator('#teams-transcription-panel');
    await expect(transcriptionPanel).toBeVisible();

    // 查找并点击开始按钮
    const startButton = transcriptionPanel.locator('button').filter({ hasText: /开始|Start/ });
    
    if (await startButton.count() > 0) {
      await startButton.click();
      
      // 验证状态变化
      const status = transcriptionPanel.locator('#transcription-status');
      await expect(status).toBeVisible();
      
      // 检查状态是否变为活跃
      await expect(status).toHaveClass(/status-active/, { timeout: 5000 });
    }
  });

  test('应该能够捕获和显示Teams字幕内容', async () => {
    await page.goto('/test-page.html');
    await page.waitForLoadState('networkidle');
    await page.waitForTimeout(3000);

    // 确保扩展面板存在
    const transcriptionPanel = page.locator('#teams-transcription-panel');
    await expect(transcriptionPanel).toBeVisible();

    // 模拟会议开始
    await page.click('#simulate-meeting');
    await page.waitForTimeout(1000);

    // 添加一些字幕内容
    await page.click('#add-caption');
    await page.waitForTimeout(500);
    await page.click('#add-caption');
    await page.waitForTimeout(500);

    // 验证转录区域是否显示了内容
    const transcriptArea = transcriptionPanel.locator('.transcript-area');
    await expect(transcriptArea).toBeVisible();

    // 检查是否有转录条目
    const transcriptEntries = transcriptArea.locator('.transcript-entry');
    
    // 等待一段时间让扩展处理字幕
    await page.waitForTimeout(2000);
    
    // 验证至少有一些转录内容（可能需要调整期望值）
    const entryCount = await transcriptEntries.count();
    console.log(`Found ${entryCount} transcript entries`);
  });

  test('应该正确适应主题切换（CSS变量测试）', async () => {
    await page.goto('/test-page.html');
    await page.waitForLoadState('networkidle');
    await page.waitForTimeout(3000);

    // 确保扩展面板存在
    const transcriptionPanel = page.locator('#teams-transcription-panel');
    await expect(transcriptionPanel).toBeVisible();

    // 获取亮色主题下的背景色
    const lightBgColor = await transcriptionPanel.evaluate(el => {
      return window.getComputedStyle(el).backgroundColor;
    });

    // 切换到暗色主题
    await page.click('#toggle-theme');
    await page.waitForTimeout(1000); // 等待主题切换动画

    // 获取暗色主题下的背景色
    const darkBgColor = await transcriptionPanel.evaluate(el => {
      return window.getComputedStyle(el).backgroundColor;
    });

    // 验证背景色确实发生了变化
    expect(lightBgColor).not.toBe(darkBgColor);
    console.log(`Light theme bg: ${lightBgColor}, Dark theme bg: ${darkBgColor}`);

    // 验证文字颜色也相应变化
    const header = transcriptionPanel.locator('.transcription-header');
    const lightTextColor = await header.evaluate(el => {
      return window.getComputedStyle(el).color;
    });

    // 切换回亮色主题
    await page.click('#toggle-theme');
    await page.waitForTimeout(1000);

    const lightTextColorAfter = await header.evaluate(el => {
      return window.getComputedStyle(el).color;
    });

    // 验证颜色恢复
    expect(lightTextColor).toBe(lightTextColorAfter);
  });

  test('应该正确处理面板的拖拽和最小化功能', async () => {
    await page.goto('/test-page.html');
    await page.waitForLoadState('networkidle');
    await page.waitForTimeout(3000);

    const transcriptionPanel = page.locator('#teams-transcription-panel');
    await expect(transcriptionPanel).toBeVisible();

    // 获取面板初始位置
    const initialBox = await transcriptionPanel.boundingBox();
    expect(initialBox).toBeTruthy();

    // 测试拖拽功能（如果实现了的话）
    const header = transcriptionPanel.locator('.transcription-header');

    // 尝试拖拽面板
    await header.hover();
    await page.mouse.down();
    await page.mouse.move(initialBox.x + 100, initialBox.y + 50);
    await page.mouse.up();

    await page.waitForTimeout(500);

    // 验证面板位置是否改变（如果拖拽功能已实现）
    const newBox = await transcriptionPanel.boundingBox();
    console.log(`Initial position: ${initialBox.x}, ${initialBox.y}`);
    console.log(`New position: ${newBox.x}, ${newBox.y}`);
  });

  test('应该正确处理错误状态和恢复', async () => {
    await page.goto('/test-page.html');
    await page.waitForLoadState('networkidle');
    await page.waitForTimeout(3000);

    const transcriptionPanel = page.locator('#teams-transcription-panel');
    await expect(transcriptionPanel).toBeVisible();

    // 模拟错误情况（例如，移除字幕容器）
    await page.evaluate(() => {
      const captionsContainer = document.querySelector('[data-tid="closed-captions-container"]');
      if (captionsContainer) {
        captionsContainer.remove();
      }
    });

    await page.waitForTimeout(2000);

    // 验证扩展是否正确处理了错误状态
    const status = transcriptionPanel.locator('#transcription-status');
    if (await status.count() > 0) {
      const statusText = await status.textContent();
      console.log(`Status after error: ${statusText}`);
    }

    // 恢复字幕容器
    await page.evaluate(() => {
      const container = document.createElement('div');
      container.setAttribute('data-tid', 'closed-captions-container');
      container.id = 'captions-container';
      container.innerHTML = '<div class="caption-entry"><span class="caption-speaker">Test:</span><span class="caption-text">Recovery test</span></div>';
      document.body.appendChild(container);
    });

    await page.waitForTimeout(2000);

    // 验证扩展是否恢复正常
    console.log('Error recovery test completed');
  });

  test('应该正确保存和加载转录记录', async () => {
    await page.goto('/test-page.html');
    await page.waitForLoadState('networkidle');
    await page.waitForTimeout(3000);

    const transcriptionPanel = page.locator('#teams-transcription-panel');
    await expect(transcriptionPanel).toBeVisible();

    // 模拟一些转录活动
    await page.click('#simulate-meeting');
    await page.waitForTimeout(1000);

    // 添加多个字幕
    for (let i = 0; i < 3; i++) {
      await page.click('#add-caption');
      await page.waitForTimeout(500);
    }

    // 等待扩展处理
    await page.waitForTimeout(3000);

    // 检查是否有保存按钮或自动保存功能
    const saveButton = transcriptionPanel.locator('button').filter({ hasText: /保存|Save/ });
    if (await saveButton.count() > 0) {
      await saveButton.click();
      await page.waitForTimeout(1000);
    }

    // 刷新页面测试数据持久性
    await page.reload();
    await page.waitForLoadState('networkidle');
    await page.waitForTimeout(3000);

    // 验证数据是否被正确恢复
    const newTranscriptionPanel = page.locator('#teams-transcription-panel');
    await expect(newTranscriptionPanel).toBeVisible();

    console.log('Save and load test completed');
  });
});
