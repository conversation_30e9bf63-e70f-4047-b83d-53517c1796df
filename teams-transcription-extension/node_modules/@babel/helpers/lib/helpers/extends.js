"use strict";

Object.defineProperty(exports, "__esModule", {
  value: true
});
exports.default = _extends;
function _extends() {
  exports.default = _extends = Object.assign ? Object.assign.bind() : function (target) {
    for (var i = 1; i < arguments.length; i++) {
      var source = arguments[i];
      for (var key in source) {
        if (Object.prototype.hasOwnProperty.call(source, key)) {
          target[key] = source[key];
        }
      }
    }
    return target;
  };
  return _extends.apply(null, arguments);
}

//# sourceMappingURL=extends.js.map
