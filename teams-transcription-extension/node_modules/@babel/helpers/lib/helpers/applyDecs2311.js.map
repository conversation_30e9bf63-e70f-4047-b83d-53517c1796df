{"version": 3, "names": ["_checkInRHS", "require", "_setFunctionName", "_to<PERSON><PERSON><PERSON><PERSON><PERSON>", "applyDecs2311", "targetClass", "classDecs", "memberDecs", "classDecsHaveThis", "instanceBrand", "parentClass", "symbolMetadata", "Symbol", "metadata", "defineProperty", "Object", "create", "existingNonFields", "hasClassDecs", "length", "_", "createRunInitializers", "initializers", "useStaticThis", "hasValue", "thisArg", "value", "i", "apply", "assertCallable", "fn", "hint1", "hint2", "throwUndefined", "TypeError", "applyDec", "Class", "decInfo", "decoratorsHaveThis", "name", "kind", "ret", "isStatic", "isPrivate", "isField", "hasPrivateBrand", "assertInstanceIfPrivate", "target", "decs", "concat", "decVal", "isClass", "isAccessor", "isGetter", "isSetter", "isMethod", "_bindPropCall", "before", "_this", "desc", "call", "init", "key", "get", "setFunctionName", "set", "getOwnPropertyDescriptor", "Error", "newValue", "dec", "decT<PERSON>", "decoratorFinishedRef", "ctx", "addInitializer", "initializer", "v", "push", "bind", "access", "has", "unshift", "splice", "applyMemberDecs", "protoInitializers", "staticInitializers", "pushInitializers", "applyMemberDecsOfKind", "kindOnly", "prototype", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "checkInRHS", "defineMetadata", "configurable", "enumerable", "undefined", "e", "c"], "sources": ["../../src/helpers/applyDecs2311.ts"], "sourcesContent": ["/* @minVersion 7.24.0 */\n/* @mangleFns */\n\n/* eslint-disable @typescript-eslint/no-unnecessary-type-assertion -- `typescript-eslint` complains when using `!` */\n\nimport checkInRHS from \"./checkInRHS.ts\";\nimport setFunctionName from \"./setFunctionName.ts\";\nimport toPropertyKey from \"./toPropertyKey.ts\";\n\nconst enum PROP_KIND {\n  FIELD = 0,\n  ACCESSOR = 1,\n  METHOD = 2,\n  GETTER = 3,\n  SETTER = 4,\n  CLASS = 5,\n  KIND_MASK = 7, // 0b111\n\n  STATIC = 8,\n\n  DECORATORS_HAVE_THIS = 16,\n}\n\ntype DecoratorFinishedRef = { v?: number };\ntype DecoratorContextAccess = {\n  get?: (target: object) => any;\n  set?: (target: object, value: any) => void;\n  has: (target: object) => boolean;\n};\ntype DecoratorContext = {\n  kind: \"accessor\" | \"method\" | \"getter\" | \"setter\" | \"field\" | \"class\";\n  name: string | symbol;\n  static?: boolean;\n  private?: boolean;\n  access?: DecoratorContextAccess;\n  metadata?: any;\n  addInitializer?: (initializer: Function) => void;\n};\ntype DecoratorInfo =\n  | [\n      decs: Function | Function[],\n      kind: PROP_KIND,\n      name: string,\n      privateGetter?: Function,\n      privateSetter?: Function,\n    ]\n  | [classDecs: Function[]];\ntype DecoratorNonFieldCheckStorage = Record<\n  string | symbol,\n  PROP_KIND.ACCESSOR | PROP_KIND.GETTER | PROP_KIND.SETTER\n>;\n/**\n  Basic usage:\n\n  applyDecs(\n    Class,\n    [\n      // member decorators\n      [\n        decs,               // dec, or array of decs, or array of this values and decs\n        0,                  // kind of value being decorated\n        'prop',             // name of public prop on class containing the value being decorated,\n        '#p',               // the name of the private property (if is private, void 0 otherwise),\n      ]\n    ],\n    [\n      // class decorators\n      dec1, dec2\n    ]\n  )\n  ```\n\n  Fully transpiled example:\n\n  ```js\n  @dec\n  class Class {\n    @dec\n    a = 123;\n\n    @dec\n    #a = 123;\n\n    @dec\n    @dec2\n    accessor b = 123;\n\n    @dec\n    accessor #b = 123;\n\n    @dec\n    c() { console.log('c'); }\n\n    @dec\n    #c() { console.log('privC'); }\n\n    @dec\n    get d() { console.log('d'); }\n\n    @dec\n    get #d() { console.log('privD'); }\n\n    @dec\n    set e(v) { console.log('e'); }\n\n    @dec\n    set #e(v) { console.log('privE'); }\n  }\n\n\n  // becomes\n  let initializeInstance;\n  let initializeClass;\n\n  let initA;\n  let initPrivA;\n\n  let initB;\n  let initPrivB, getPrivB, setPrivB;\n\n  let privC;\n  let privD;\n  let privE;\n\n  let Class;\n  class _Class {\n    static {\n      let ret = applyDecs(\n        this,\n        [\n          [dec, 0, 'a'],\n          [dec, 0, 'a', (i) => i.#a, (i, v) => i.#a = v],\n          [[dec, dec2], 1, 'b'],\n          [dec, 1, 'b', (i) => i.#privBData, (i, v) => i.#privBData = v],\n          [dec, 2, 'c'],\n          [dec, 2, 'c', () => console.log('privC')],\n          [dec, 3, 'd'],\n          [dec, 3, 'd', () => console.log('privD')],\n          [dec, 4, 'e'],\n          [dec, 4, 'e', () => console.log('privE')],\n        ],\n        [\n          dec\n        ]\n      );\n\n      initA = ret[0];\n\n      initPrivA = ret[1];\n\n      initB = ret[2];\n\n      initPrivB = ret[3];\n      getPrivB = ret[4];\n      setPrivB = ret[5];\n\n      privC = ret[6];\n\n      privD = ret[7];\n\n      privE = ret[8];\n\n      initializeInstance = ret[9];\n\n      Class = ret[10]\n\n      initializeClass = ret[11];\n    }\n\n    a = (initializeInstance(this), initA(this, 123));\n\n    #a = initPrivA(this, 123);\n\n    #bData = initB(this, 123);\n    get b() { return this.#bData }\n    set b(v) { this.#bData = v }\n\n    #privBData = initPrivB(this, 123);\n    get #b() { return getPrivB(this); }\n    set #b(v) { setPrivB(this, v); }\n\n    c() { console.log('c'); }\n\n    #c(...args) { return privC(this, ...args) }\n\n    get d() { console.log('d'); }\n\n    get #d() { return privD(this); }\n\n    set e(v) { console.log('e'); }\n\n    set #e(v) { privE(this, v); }\n  }\n\n  initializeClass(Class);\n */\n\nexport default /* @no-mangle */ function applyDecs2311(\n  targetClass: any,\n  classDecs: Function[],\n  memberDecs: DecoratorInfo[],\n  classDecsHaveThis: number,\n  instanceBrand: Function,\n  parentClass: any,\n) {\n  var symbolMetadata = Symbol.metadata || Symbol[\"for\"](\"Symbol.metadata\");\n  var defineProperty = Object.defineProperty;\n  var create = Object.create;\n  var metadata: any;\n  // Use both as and satisfies to ensure that we only use non-zero values\n  var existingNonFields = [create(null), create(null)] as [\n    DecoratorNonFieldCheckStorage,\n    DecoratorNonFieldCheckStorage,\n  ];\n  var hasClassDecs = classDecs.length;\n  // This is a temporary variable for smaller helper size\n  var _: any;\n\n  function createRunInitializers(\n    initializers: Function[],\n    useStaticThis?: 0 | 1 | boolean,\n    hasValue?: 0 | 1,\n  ) {\n    return function (thisArg: any, value?: any) {\n      if (useStaticThis) {\n        value = thisArg;\n        thisArg = targetClass;\n      }\n      for (var i = 0; i < initializers.length; i++) {\n        value = initializers[i].apply(thisArg, hasValue ? [value] : []);\n      }\n      return hasValue ? value : thisArg;\n    };\n  }\n\n  function assertCallable(\n    fn: any,\n    hint1: string,\n    hint2?: string,\n    throwUndefined?: boolean,\n  ) {\n    if (typeof fn !== \"function\") {\n      if (throwUndefined || fn !== void 0) {\n        throw new TypeError(\n          hint1 +\n            \" must \" +\n            (hint2 || \"be\") +\n            \" a function\" +\n            (throwUndefined ? \"\" : \" or undefined\"),\n        );\n      }\n    }\n    return fn;\n  }\n\n  /* @no-mangle */\n  function applyDec(\n    Class: any,\n    decInfo: DecoratorInfo,\n    decoratorsHaveThis: 0 | PROP_KIND.DECORATORS_HAVE_THIS,\n    name: string | symbol,\n    kind: PROP_KIND,\n    initializers: Function[],\n    ret?: Function[],\n    isStatic?: boolean,\n    isPrivate?: boolean,\n    isField?: 0 | 1,\n    hasPrivateBrand?: Function,\n  ) {\n    function assertInstanceIfPrivate(target: any) {\n      if (!hasPrivateBrand!(target)) {\n        throw new TypeError(\n          \"Attempted to access private element on non-instance\",\n        );\n      }\n    }\n\n    var decs = ([] as Function[]).concat(decInfo[0]),\n      decVal = decInfo[3],\n      isClass = !ret;\n\n    var isAccessor = kind === PROP_KIND.ACCESSOR;\n    var isGetter = kind === PROP_KIND.GETTER;\n    var isSetter = kind === PROP_KIND.SETTER;\n    var isMethod = kind === PROP_KIND.METHOD;\n\n    function _bindPropCall(\n      name: keyof PropertyDescriptor,\n      useStaticThis: 0 | 1 | boolean,\n      before?: Function,\n    ) {\n      return function (_this: any, value?: any) {\n        if (useStaticThis) {\n          value = _this;\n          _this = Class;\n        }\n        if (before) {\n          before(_this);\n        }\n        // eslint-disable-next-line @typescript-eslint/no-use-before-define\n        return desc[name].call(_this, value);\n      };\n    }\n\n    if (!isClass) {\n      var desc: PropertyDescriptor = {},\n        init: Function[] = [],\n        key: \"get\" | \"set\" | \"value\" = isGetter\n          ? \"get\"\n          : isSetter || isAccessor\n            ? \"set\"\n            : \"value\";\n\n      if (isPrivate) {\n        if (isField || isAccessor) {\n          desc = {\n            get: setFunctionName(\n              function (this: any) {\n                return decVal!(this);\n              },\n              name,\n              \"get\",\n            ),\n            set: function (this: any, value: any) {\n              decInfo[4]!(this, value);\n            },\n          };\n        } else {\n          desc[key] = decVal;\n        }\n\n        if (!isField) {\n          setFunctionName(desc[key], name, isMethod ? \"\" : key);\n        }\n      } else if (!isField) {\n        desc = Object.getOwnPropertyDescriptor(Class, name)!;\n      }\n\n      if (!isField && !isPrivate) {\n        _ = existingNonFields[+isStatic!][name];\n        // flag is 1, 3, or 4; kind is 0, 1, 2, 3, or 4\n        // flag ^ kind is 7 if and only if one of them is 3 and the other one is 4.\n        if (_ && (_ ^ kind) !== 7) {\n          throw new Error(\n            \"Decorating two elements with the same name (\" +\n              desc[key].name +\n              \") is not supported yet\",\n          );\n        }\n        // We use PROP_KIND.ACCESSOR to mark a name as \"fully used\":\n        // either a get/set pair, or a non-getter/setter.\n        existingNonFields[+isStatic!][name] =\n          kind < PROP_KIND.GETTER\n            ? PROP_KIND.ACCESSOR\n            : (kind as PROP_KIND.GETTER | PROP_KIND.SETTER);\n      }\n    }\n\n    var newValue = Class;\n\n    for (var i = decs.length - 1; i >= 0; i -= decoratorsHaveThis ? 2 : 1) {\n      var dec = assertCallable(decs[i], \"A decorator\", \"be\", true) as Function,\n        decThis = decoratorsHaveThis ? decs[i - 1] : void 0;\n\n      var decoratorFinishedRef: DecoratorFinishedRef = {};\n      var ctx: DecoratorContext = {\n        kind: [\"field\", \"accessor\", \"method\", \"getter\", \"setter\", \"class\"][\n          kind\n        ] as any,\n\n        name: name,\n        metadata: metadata,\n        addInitializer: function (\n          decoratorFinishedRef: DecoratorFinishedRef,\n          initializer: Function,\n        ) {\n          if (decoratorFinishedRef.v) {\n            throw new TypeError(\n              \"attempted to call addInitializer after decoration was finished\",\n            );\n          }\n          assertCallable(initializer, \"An initializer\", \"be\", true);\n          initializers.push(initializer);\n        }.bind(null, decoratorFinishedRef),\n      };\n\n      if (isClass) {\n        _ = dec.call(decThis, newValue, ctx);\n        decoratorFinishedRef.v = 1;\n\n        if (assertCallable(_, \"class decorators\", \"return\")) {\n          newValue = _;\n        }\n      } else {\n        ctx[\"static\"] = isStatic;\n        ctx[\"private\"] = isPrivate;\n\n        _ = ctx.access = {\n          has: isPrivate\n            ? // @ts-expect-error no thisArg\n              hasPrivateBrand.bind()\n            : function (target: object) {\n                return name in target;\n              },\n        };\n\n        if (!isSetter) {\n          _.get = isPrivate\n            ? isMethod\n              ? function (_this: any) {\n                  assertInstanceIfPrivate(_this);\n                  return desc.value;\n                }\n              : _bindPropCall(\"get\", 0, assertInstanceIfPrivate)\n            : function (target: any) {\n                return target[name];\n              };\n        }\n        if (!isMethod && !isGetter) {\n          _.set = isPrivate\n            ? _bindPropCall(\"set\", 0, assertInstanceIfPrivate)\n            : function (target: any, v: any) {\n                target[name] = v;\n              };\n        }\n\n        newValue = dec.call(\n          decThis,\n          isAccessor\n            ? {\n                get: desc!.get,\n                set: desc!.set,\n              }\n            : desc![key!],\n          ctx,\n        );\n        decoratorFinishedRef.v = 1;\n\n        if (isAccessor) {\n          if (typeof newValue === \"object\" && newValue) {\n            if ((_ = assertCallable(newValue.get, \"accessor.get\"))) {\n              desc!.get = _;\n            }\n            if ((_ = assertCallable(newValue.set, \"accessor.set\"))) {\n              desc!.set = _;\n            }\n            if ((_ = assertCallable(newValue.init, \"accessor.init\"))) {\n              init!.unshift(_);\n            }\n          } else if (newValue !== void 0) {\n            throw new TypeError(\n              \"accessor decorators must return an object with get, set, or init properties or undefined\",\n            );\n          }\n        } else if (\n          assertCallable(\n            newValue,\n            (isField ? \"field\" : \"method\") + \" decorators\",\n            \"return\",\n          )\n        ) {\n          if (isField) {\n            init!.unshift(newValue);\n          } else {\n            desc![key!] = newValue;\n          }\n        }\n      }\n    }\n\n    // isField || isAccessor\n    if (kind < PROP_KIND.METHOD) {\n      ret!.push(\n        // init\n        createRunInitializers(init!, isStatic, 1),\n        // init_extra\n        createRunInitializers(initializers, isStatic, 0),\n      );\n    }\n\n    if (!isField && !isClass) {\n      if (isPrivate) {\n        if (isAccessor) {\n          // get and set should be returned before init_extra\n          ret!.splice(\n            -1,\n            0,\n            _bindPropCall(\"get\", isStatic!),\n            _bindPropCall(\"set\", isStatic!),\n          );\n        } else {\n          ret!.push(\n            isMethod\n              ? desc![key!]\n              : // Equivalent to `Function.call`, just to reduce code size\n                assertCallable.call.bind(desc![key!]),\n          );\n        }\n      } else {\n        defineProperty(Class, name, desc!);\n      }\n    }\n    return newValue;\n  }\n\n  /* @no-mangle */\n  function applyMemberDecs() {\n    var ret: Function[] = [];\n    var protoInitializers: Function[];\n    var staticInitializers: Function[];\n\n    var pushInitializers = function (initializers: Function[]) {\n      if (initializers) {\n        ret.push(createRunInitializers(initializers));\n      }\n    };\n\n    var applyMemberDecsOfKind = function (\n      isStatic: PROP_KIND.STATIC | 0,\n      isField: 0 | 1,\n    ) {\n      for (var i = 0; i < memberDecs.length; i++) {\n        var decInfo = memberDecs[i];\n\n        var kind = decInfo[1]!;\n        var kindOnly: PROP_KIND = kind & PROP_KIND.KIND_MASK;\n        if (\n          // eslint-disable-next-line @typescript-eslint/no-unsafe-enum-comparison, eqeqeq\n          (kind & PROP_KIND.STATIC) == isStatic &&\n          // @ts-expect-error comparing a boolean with 0 | 1\n          // eslint-disable-next-line eqeqeq\n          !kindOnly == isField\n        ) {\n          var name = decInfo[2];\n          var isPrivate = !!decInfo[3];\n\n          var decoratorsHaveThis: 0 | PROP_KIND.DECORATORS_HAVE_THIS =\n            kind & PROP_KIND.DECORATORS_HAVE_THIS;\n\n          applyDec(\n            isStatic ? targetClass : targetClass.prototype,\n            decInfo,\n            decoratorsHaveThis,\n            isPrivate ? \"#\" + name : (toPropertyKey(name) as string),\n            kindOnly,\n            kindOnly < PROP_KIND.METHOD // isField || isAccessor\n              ? /* fieldInitializers */ []\n              : isStatic\n                ? (staticInitializers = staticInitializers || [])\n                : (protoInitializers = protoInitializers || []),\n            ret,\n            !!isStatic,\n            isPrivate,\n            isField,\n            isStatic && isPrivate\n              ? function (_: any) {\n                  return checkInRHS(_) === targetClass;\n                }\n              : instanceBrand,\n          );\n        }\n      }\n    };\n\n    applyMemberDecsOfKind(PROP_KIND.STATIC, 0);\n    applyMemberDecsOfKind(0, 0);\n    applyMemberDecsOfKind(PROP_KIND.STATIC, 1);\n    applyMemberDecsOfKind(0, 1);\n\n    pushInitializers(protoInitializers!);\n    pushInitializers(staticInitializers!);\n    return ret;\n  }\n\n  function defineMetadata(Class: any) {\n    return defineProperty(Class, symbolMetadata, {\n      configurable: true,\n      enumerable: true,\n      value: metadata,\n    });\n  }\n\n  if (parentClass !== undefined) {\n    metadata = parentClass[symbolMetadata];\n  }\n  metadata = create(metadata == null ? null : metadata);\n  _ = applyMemberDecs();\n  if (!hasClassDecs) defineMetadata(targetClass);\n  return {\n    e: _,\n    // Lazily apply class decorations so that member init locals can be properly bound.\n    get c() {\n      // The transformer will not emit assignment when there are no class decorators,\n      // so we don't have to return an empty array here.\n      var initializers: Function[] = [];\n      return (\n        hasClassDecs && [\n          defineMetadata(\n            (targetClass = applyDec(\n              targetClass,\n              [classDecs],\n              classDecsHaveThis,\n              targetClass.name,\n              PROP_KIND.CLASS,\n              initializers,\n            )),\n          ),\n          createRunInitializers(initializers, 1),\n        ]\n      );\n    },\n  };\n}\n"], "mappings": ";;;;;;AAKA,IAAAA,WAAA,GAAAC,OAAA;AACA,IAAAC,gBAAA,GAAAD,OAAA;AACA,IAAAE,cAAA,GAAAF,OAAA;AA8LgC,SAASG,aAAaA,CACpDC,WAAgB,EAChBC,SAAqB,EACrBC,UAA2B,EAC3BC,iBAAyB,EACzBC,aAAuB,EACvBC,WAAgB,EAChB;EACA,IAAIC,cAAc,GAAGC,MAAM,CAACC,QAAQ,IAAID,MAAM,CAAC,KAAK,CAAC,CAAC,iBAAiB,CAAC;EACxE,IAAIE,cAAc,GAAGC,MAAM,CAACD,cAAc;EAC1C,IAAIE,MAAM,GAAGD,MAAM,CAACC,MAAM;EAC1B,IAAIH,QAAa;EAEjB,IAAII,iBAAiB,GAAG,CAACD,MAAM,CAAC,IAAI,CAAC,EAAEA,MAAM,CAAC,IAAI,CAAC,CAGlD;EACD,IAAIE,YAAY,GAAGZ,SAAS,CAACa,MAAM;EAEnC,IAAIC,CAAM;EAEV,SAASC,qBAAqBA,CAC5BC,YAAwB,EACxBC,aAA+B,EAC/BC,QAAgB,EAChB;IACA,OAAO,UAAUC,OAAY,EAAEC,KAAW,EAAE;MAC1C,IAAIH,aAAa,EAAE;QACjBG,KAAK,GAAGD,OAAO;QACfA,OAAO,GAAGpB,WAAW;MACvB;MACA,KAAK,IAAIsB,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGL,YAAY,CAACH,MAAM,EAAEQ,CAAC,EAAE,EAAE;QAC5CD,KAAK,GAAGJ,YAAY,CAACK,CAAC,CAAC,CAACC,KAAK,CAACH,OAAO,EAAED,QAAQ,GAAG,CAACE,KAAK,CAAC,GAAG,EAAE,CAAC;MACjE;MACA,OAAOF,QAAQ,GAAGE,KAAK,GAAGD,OAAO;IACnC,CAAC;EACH;EAEA,SAASI,cAAcA,CACrBC,EAAO,EACPC,KAAa,EACbC,KAAc,EACdC,cAAwB,EACxB;IACA,IAAI,OAAOH,EAAE,KAAK,UAAU,EAAE;MAC5B,IAAIG,cAAc,IAAIH,EAAE,KAAK,KAAK,CAAC,EAAE;QACnC,MAAM,IAAII,SAAS,CACjBH,KAAK,GACH,QAAQ,IACPC,KAAK,IAAI,IAAI,CAAC,GACf,aAAa,IACZC,cAAc,GAAG,EAAE,GAAG,eAAe,CAC1C,CAAC;MACH;IACF;IACA,OAAOH,EAAE;EACX;EAGA,SAASK,QAAQA,CACfC,KAAU,EACVC,OAAsB,EACtBC,kBAAsD,EACtDC,IAAqB,EACrBC,IAAe,EACflB,YAAwB,EACxBmB,GAAgB,EAChBC,QAAkB,EAClBC,SAAmB,EACnBC,OAAe,EACfC,eAA0B,EAC1B;IACA,SAASC,uBAAuBA,CAACC,MAAW,EAAE;MAC5C,IAAI,CAACF,eAAe,CAAEE,MAAM,CAAC,EAAE;QAC7B,MAAM,IAAIb,SAAS,CACjB,qDACF,CAAC;MACH;IACF;IAEA,IAAIc,IAAI,GAAI,EAAE,CAAgBC,MAAM,CAACZ,OAAO,CAAC,CAAC,CAAC,CAAC;MAC9Ca,MAAM,GAAGb,OAAO,CAAC,CAAC,CAAC;MACnBc,OAAO,GAAG,CAACV,GAAG;IAEhB,IAAIW,UAAU,GAAGZ,IAAI,MAAuB;IAC5C,IAAIa,QAAQ,GAAGb,IAAI,MAAqB;IACxC,IAAIc,QAAQ,GAAGd,IAAI,MAAqB;IACxC,IAAIe,QAAQ,GAAGf,IAAI,MAAqB;IAExC,SAASgB,aAAaA,CACpBjB,IAA8B,EAC9BhB,aAA8B,EAC9BkC,MAAiB,EACjB;MACA,OAAO,UAAUC,KAAU,EAAEhC,KAAW,EAAE;QACxC,IAAIH,aAAa,EAAE;UACjBG,KAAK,GAAGgC,KAAK;UACbA,KAAK,GAAGtB,KAAK;QACf;QACA,IAAIqB,MAAM,EAAE;UACVA,MAAM,CAACC,KAAK,CAAC;QACf;QAEA,OAAOC,IAAI,CAACpB,IAAI,CAAC,CAACqB,IAAI,CAACF,KAAK,EAAEhC,KAAK,CAAC;MACtC,CAAC;IACH;IAEA,IAAI,CAACyB,OAAO,EAAE;MACZ,IAAIQ,IAAwB,GAAG,CAAC,CAAC;QAC/BE,IAAgB,GAAG,EAAE;QACrBC,GAA4B,GAAGT,QAAQ,GACnC,KAAK,GACLC,QAAQ,IAAIF,UAAU,GACpB,KAAK,GACL,OAAO;MAEf,IAAIT,SAAS,EAAE;QACb,IAAIC,OAAO,IAAIQ,UAAU,EAAE;UACzBO,IAAI,GAAG;YACLI,GAAG,EAAE,IAAAC,wBAAe,EAClB,YAAqB;cACnB,OAAOd,MAAM,CAAE,IAAI,CAAC;YACtB,CAAC,EACDX,IAAI,EACJ,KACF,CAAC;YACD0B,GAAG,EAAE,SAAAA,CAAqBvC,KAAU,EAAE;cACpCW,OAAO,CAAC,CAAC,CAAC,CAAE,IAAI,EAAEX,KAAK,CAAC;YAC1B;UACF,CAAC;QACH,CAAC,MAAM;UACLiC,IAAI,CAACG,GAAG,CAAC,GAAGZ,MAAM;QACpB;QAEA,IAAI,CAACN,OAAO,EAAE;UACZ,IAAAoB,wBAAe,EAACL,IAAI,CAACG,GAAG,CAAC,EAAEvB,IAAI,EAAEgB,QAAQ,GAAG,EAAE,GAAGO,GAAG,CAAC;QACvD;MACF,CAAC,MAAM,IAAI,CAAClB,OAAO,EAAE;QACnBe,IAAI,GAAG5C,MAAM,CAACmD,wBAAwB,CAAC9B,KAAK,EAAEG,IAAI,CAAE;MACtD;MAEA,IAAI,CAACK,OAAO,IAAI,CAACD,SAAS,EAAE;QAC1BvB,CAAC,GAAGH,iBAAiB,CAAC,CAACyB,QAAS,CAAC,CAACH,IAAI,CAAC;QAGvC,IAAInB,CAAC,IAAI,CAACA,CAAC,GAAGoB,IAAI,MAAM,CAAC,EAAE;UACzB,MAAM,IAAI2B,KAAK,CACb,8CAA8C,GAC5CR,IAAI,CAACG,GAAG,CAAC,CAACvB,IAAI,GACd,wBACJ,CAAC;QACH;QAGAtB,iBAAiB,CAAC,CAACyB,QAAS,CAAC,CAACH,IAAI,CAAC,GACjCC,IAAI,IAAmB,OAElBA,IAA4C;MACrD;IACF;IAEA,IAAI4B,QAAQ,GAAGhC,KAAK;IAEpB,KAAK,IAAIT,CAAC,GAAGqB,IAAI,CAAC7B,MAAM,GAAG,CAAC,EAAEQ,CAAC,IAAI,CAAC,EAAEA,CAAC,IAAIW,kBAAkB,GAAG,CAAC,GAAG,CAAC,EAAE;MACrE,IAAI+B,GAAG,GAAGxC,cAAc,CAACmB,IAAI,CAACrB,CAAC,CAAC,EAAE,aAAa,EAAE,IAAI,EAAE,IAAI,CAAa;QACtE2C,OAAO,GAAGhC,kBAAkB,GAAGU,IAAI,CAACrB,CAAC,GAAG,CAAC,CAAC,GAAG,KAAK,CAAC;MAErD,IAAI4C,oBAA0C,GAAG,CAAC,CAAC;MACnD,IAAIC,GAAqB,GAAG;QAC1BhC,IAAI,EAAE,CAAC,OAAO,EAAE,UAAU,EAAE,QAAQ,EAAE,QAAQ,EAAE,QAAQ,EAAE,OAAO,CAAC,CAChEA,IAAI,CACE;QAERD,IAAI,EAAEA,IAAI;QACV1B,QAAQ,EAAEA,QAAQ;QAClB4D,cAAc,EAAE,UACdF,oBAA0C,EAC1CG,WAAqB,EACrB;UACA,IAAIH,oBAAoB,CAACI,CAAC,EAAE;YAC1B,MAAM,IAAIzC,SAAS,CACjB,gEACF,CAAC;UACH;UACAL,cAAc,CAAC6C,WAAW,EAAE,gBAAgB,EAAE,IAAI,EAAE,IAAI,CAAC;UACzDpD,YAAY,CAACsD,IAAI,CAACF,WAAW,CAAC;QAChC,CAAC,CAACG,IAAI,CAAC,IAAI,EAAEN,oBAAoB;MACnC,CAAC;MAED,IAAIpB,OAAO,EAAE;QACX/B,CAAC,GAAGiD,GAAG,CAACT,IAAI,CAACU,OAAO,EAAEF,QAAQ,EAAEI,GAAG,CAAC;QACpCD,oBAAoB,CAACI,CAAC,GAAG,CAAC;QAE1B,IAAI9C,cAAc,CAACT,CAAC,EAAE,kBAAkB,EAAE,QAAQ,CAAC,EAAE;UACnDgD,QAAQ,GAAGhD,CAAC;QACd;MACF,CAAC,MAAM;QACLoD,GAAG,CAAC,QAAQ,CAAC,GAAG9B,QAAQ;QACxB8B,GAAG,CAAC,SAAS,CAAC,GAAG7B,SAAS;QAE1BvB,CAAC,GAAGoD,GAAG,CAACM,MAAM,GAAG;UACfC,GAAG,EAAEpC,SAAS,GAEVE,eAAe,CAACgC,IAAI,CAAC,CAAC,GACtB,UAAU9B,MAAc,EAAE;YACxB,OAAOR,IAAI,IAAIQ,MAAM;UACvB;QACN,CAAC;QAED,IAAI,CAACO,QAAQ,EAAE;UACblC,CAAC,CAAC2C,GAAG,GAAGpB,SAAS,GACbY,QAAQ,GACN,UAAUG,KAAU,EAAE;YACpBZ,uBAAuB,CAACY,KAAK,CAAC;YAC9B,OAAOC,IAAI,CAACjC,KAAK;UACnB,CAAC,GACD8B,aAAa,CAAC,KAAK,EAAE,CAAC,EAAEV,uBAAuB,CAAC,GAClD,UAAUC,MAAW,EAAE;YACrB,OAAOA,MAAM,CAACR,IAAI,CAAC;UACrB,CAAC;QACP;QACA,IAAI,CAACgB,QAAQ,IAAI,CAACF,QAAQ,EAAE;UAC1BjC,CAAC,CAAC6C,GAAG,GAAGtB,SAAS,GACba,aAAa,CAAC,KAAK,EAAE,CAAC,EAAEV,uBAAuB,CAAC,GAChD,UAAUC,MAAW,EAAE4B,CAAM,EAAE;YAC7B5B,MAAM,CAACR,IAAI,CAAC,GAAGoC,CAAC;UAClB,CAAC;QACP;QAEAP,QAAQ,GAAGC,GAAG,CAACT,IAAI,CACjBU,OAAO,EACPlB,UAAU,GACN;UACEW,GAAG,EAAEJ,IAAI,CAAEI,GAAG;UACdE,GAAG,EAAEN,IAAI,CAAEM;QACb,CAAC,GACDN,IAAI,CAAEG,GAAG,CAAE,EACfU,GACF,CAAC;QACDD,oBAAoB,CAACI,CAAC,GAAG,CAAC;QAE1B,IAAIvB,UAAU,EAAE;UACd,IAAI,OAAOgB,QAAQ,KAAK,QAAQ,IAAIA,QAAQ,EAAE;YAC5C,IAAKhD,CAAC,GAAGS,cAAc,CAACuC,QAAQ,CAACL,GAAG,EAAE,cAAc,CAAC,EAAG;cACtDJ,IAAI,CAAEI,GAAG,GAAG3C,CAAC;YACf;YACA,IAAKA,CAAC,GAAGS,cAAc,CAACuC,QAAQ,CAACH,GAAG,EAAE,cAAc,CAAC,EAAG;cACtDN,IAAI,CAAEM,GAAG,GAAG7C,CAAC;YACf;YACA,IAAKA,CAAC,GAAGS,cAAc,CAACuC,QAAQ,CAACP,IAAI,EAAE,eAAe,CAAC,EAAG;cACxDA,IAAI,CAAEmB,OAAO,CAAC5D,CAAC,CAAC;YAClB;UACF,CAAC,MAAM,IAAIgD,QAAQ,KAAK,KAAK,CAAC,EAAE;YAC9B,MAAM,IAAIlC,SAAS,CACjB,0FACF,CAAC;UACH;QACF,CAAC,MAAM,IACLL,cAAc,CACZuC,QAAQ,EACR,CAACxB,OAAO,GAAG,OAAO,GAAG,QAAQ,IAAI,aAAa,EAC9C,QACF,CAAC,EACD;UACA,IAAIA,OAAO,EAAE;YACXiB,IAAI,CAAEmB,OAAO,CAACZ,QAAQ,CAAC;UACzB,CAAC,MAAM;YACLT,IAAI,CAAEG,GAAG,CAAE,GAAGM,QAAQ;UACxB;QACF;MACF;IACF;IAGA,IAAI5B,IAAI,IAAmB,EAAE;MAC3BC,GAAG,CAAEmC,IAAI,CAEPvD,qBAAqB,CAACwC,IAAI,EAAGnB,QAAQ,EAAE,CAAC,CAAC,EAEzCrB,qBAAqB,CAACC,YAAY,EAAEoB,QAAQ,EAAE,CAAC,CACjD,CAAC;IACH;IAEA,IAAI,CAACE,OAAO,IAAI,CAACO,OAAO,EAAE;MACxB,IAAIR,SAAS,EAAE;QACb,IAAIS,UAAU,EAAE;UAEdX,GAAG,CAAEwC,MAAM,CACT,CAAC,CAAC,EACF,CAAC,EACDzB,aAAa,CAAC,KAAK,EAAEd,QAAS,CAAC,EAC/Bc,aAAa,CAAC,KAAK,EAAEd,QAAS,CAChC,CAAC;QACH,CAAC,MAAM;UACLD,GAAG,CAAEmC,IAAI,CACPrB,QAAQ,GACJI,IAAI,CAAEG,GAAG,CAAE,GAEXjC,cAAc,CAAC+B,IAAI,CAACiB,IAAI,CAAClB,IAAI,CAAEG,GAAG,CAAE,CAC1C,CAAC;QACH;MACF,CAAC,MAAM;QACLhD,cAAc,CAACsB,KAAK,EAAEG,IAAI,EAAEoB,IAAK,CAAC;MACpC;IACF;IACA,OAAOS,QAAQ;EACjB;EAGA,SAASc,eAAeA,CAAA,EAAG;IACzB,IAAIzC,GAAe,GAAG,EAAE;IACxB,IAAI0C,iBAA6B;IACjC,IAAIC,kBAA8B;IAElC,IAAIC,gBAAgB,GAAG,SAAAA,CAAU/D,YAAwB,EAAE;MACzD,IAAIA,YAAY,EAAE;QAChBmB,GAAG,CAACmC,IAAI,CAACvD,qBAAqB,CAACC,YAAY,CAAC,CAAC;MAC/C;IACF,CAAC;IAED,IAAIgE,qBAAqB,GAAG,SAAAA,CAC1B5C,QAA8B,EAC9BE,OAAc,EACd;MACA,KAAK,IAAIjB,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGpB,UAAU,CAACY,MAAM,EAAEQ,CAAC,EAAE,EAAE;QAC1C,IAAIU,OAAO,GAAG9B,UAAU,CAACoB,CAAC,CAAC;QAE3B,IAAIa,IAAI,GAAGH,OAAO,CAAC,CAAC,CAAE;QACtB,IAAIkD,QAAmB,GAAG/C,IAAI,IAAsB;QACpD,IAEE,CAACA,IAAI,IAAmB,KAAKE,QAAQ,IAGrC,CAAC6C,QAAQ,IAAI3C,OAAO,EACpB;UACA,IAAIL,IAAI,GAAGF,OAAO,CAAC,CAAC,CAAC;UACrB,IAAIM,SAAS,GAAG,CAAC,CAACN,OAAO,CAAC,CAAC,CAAC;UAE5B,IAAIC,kBAAsD,GACxDE,IAAI,KAAiC;UAEvCL,QAAQ,CACNO,QAAQ,GAAGrC,WAAW,GAAGA,WAAW,CAACmF,SAAS,EAC9CnD,OAAO,EACPC,kBAAkB,EAClBK,SAAS,GAAG,GAAG,GAAGJ,IAAI,GAAI,IAAAkD,sBAAa,EAAClD,IAAI,CAAY,EACxDgD,QAAQ,EACRA,QAAQ,IAAmB,GACC,EAAE,GAC1B7C,QAAQ,GACL0C,kBAAkB,GAAGA,kBAAkB,IAAI,EAAE,GAC7CD,iBAAiB,GAAGA,iBAAiB,IAAI,EAAG,EACnD1C,GAAG,EACH,CAAC,CAACC,QAAQ,EACVC,SAAS,EACTC,OAAO,EACPF,QAAQ,IAAIC,SAAS,GACjB,UAAUvB,CAAM,EAAE;YAChB,OAAO,IAAAsE,mBAAU,EAACtE,CAAC,CAAC,KAAKf,WAAW;UACtC,CAAC,GACDI,aACN,CAAC;QACH;MACF;IACF,CAAC;IAED6E,qBAAqB,IAAmB,CAAC,CAAC;IAC1CA,qBAAqB,CAAC,CAAC,EAAE,CAAC,CAAC;IAC3BA,qBAAqB,IAAmB,CAAC,CAAC;IAC1CA,qBAAqB,CAAC,CAAC,EAAE,CAAC,CAAC;IAE3BD,gBAAgB,CAACF,iBAAkB,CAAC;IACpCE,gBAAgB,CAACD,kBAAmB,CAAC;IACrC,OAAO3C,GAAG;EACZ;EAEA,SAASkD,cAAcA,CAACvD,KAAU,EAAE;IAClC,OAAOtB,cAAc,CAACsB,KAAK,EAAEzB,cAAc,EAAE;MAC3CiF,YAAY,EAAE,IAAI;MAClBC,UAAU,EAAE,IAAI;MAChBnE,KAAK,EAAEb;IACT,CAAC,CAAC;EACJ;EAEA,IAAIH,WAAW,KAAKoF,SAAS,EAAE;IAC7BjF,QAAQ,GAAGH,WAAW,CAACC,cAAc,CAAC;EACxC;EACAE,QAAQ,GAAGG,MAAM,CAACH,QAAQ,IAAI,IAAI,GAAG,IAAI,GAAGA,QAAQ,CAAC;EACrDO,CAAC,GAAG8D,eAAe,CAAC,CAAC;EACrB,IAAI,CAAChE,YAAY,EAAEyE,cAAc,CAACtF,WAAW,CAAC;EAC9C,OAAO;IACL0F,CAAC,EAAE3E,CAAC;IAEJ,IAAI4E,CAACA,CAAA,EAAG;MAGN,IAAI1E,YAAwB,GAAG,EAAE;MACjC,OACEJ,YAAY,IAAI,CACdyE,cAAc,CACXtF,WAAW,GAAG8B,QAAQ,CACrB9B,WAAW,EACX,CAACC,SAAS,CAAC,EACXE,iBAAiB,EACjBH,WAAW,CAACkC,IAAI,KAEhBjB,YACF,CACF,CAAC,EACDD,qBAAqB,CAACC,YAAY,EAAE,CAAC,CAAC,CACvC;IAEL;EACF,CAAC;AACH", "ignoreList": []}