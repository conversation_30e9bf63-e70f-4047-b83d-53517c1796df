#!/usr/bin/env python3
"""
Simple script to create placeholder icons for the Teams Transcription Extension.
This creates basic PNG icons with the microphone symbol.
"""

from PIL import Image, ImageDraw, ImageFont
import os

def create_icon(size, filename):
    """Create a simple microphone icon"""
    # Create image with transparent background
    img = Image.new('RGBA', (size, size), (0, 0, 0, 0))
    draw = ImageDraw.Draw(img)
    
    # Colors
    bg_color = (70, 71, 117, 255)  # Teams purple
    mic_color = (255, 255, 255, 255)  # White
    
    # Draw background circle
    margin = size // 8
    draw.ellipse([margin, margin, size-margin, size-margin], fill=bg_color)
    
    # Draw microphone
    mic_width = size // 4
    mic_height = size // 3
    mic_x = (size - mic_width) // 2
    mic_y = (size - mic_height) // 2 - size // 8
    
    # Microphone body
    draw.rounded_rectangle([mic_x, mic_y, mic_x + mic_width, mic_y + mic_height], 
                          radius=mic_width//4, fill=mic_color)
    
    # Microphone stand
    stand_width = size // 12
    stand_height = size // 6
    stand_x = (size - stand_width) // 2
    stand_y = mic_y + mic_height
    
    draw.rectangle([stand_x, stand_y, stand_x + stand_width, stand_y + stand_height], 
                  fill=mic_color)
    
    # Base
    base_width = size // 3
    base_height = size // 16
    base_x = (size - base_width) // 2
    base_y = stand_y + stand_height
    
    draw.rectangle([base_x, base_y, base_x + base_width, base_y + base_height], 
                  fill=mic_color)
    
    # Save the image
    img.save(filename, 'PNG')
    print(f"Created {filename} ({size}x{size})")

def create_active_icon(size, filename):
    """Create an active state icon (with recording indicator)"""
    # Create image with transparent background
    img = Image.new('RGBA', (size, size), (0, 0, 0, 0))
    draw = ImageDraw.Draw(img)
    
    # Colors
    bg_color = (16, 124, 16, 255)  # Green for active
    mic_color = (255, 255, 255, 255)  # White
    indicator_color = (255, 0, 0, 255)  # Red recording dot
    
    # Draw background circle
    margin = size // 8
    draw.ellipse([margin, margin, size-margin, size-margin], fill=bg_color)
    
    # Draw microphone (same as regular icon)
    mic_width = size // 4
    mic_height = size // 3
    mic_x = (size - mic_width) // 2
    mic_y = (size - mic_height) // 2 - size // 8
    
    # Microphone body
    draw.rounded_rectangle([mic_x, mic_y, mic_x + mic_width, mic_y + mic_height], 
                          radius=mic_width//4, fill=mic_color)
    
    # Microphone stand
    stand_width = size // 12
    stand_height = size // 6
    stand_x = (size - stand_width) // 2
    stand_y = mic_y + mic_height
    
    draw.rectangle([stand_x, stand_y, stand_x + stand_width, stand_y + stand_height], 
                  fill=mic_color)
    
    # Base
    base_width = size // 3
    base_height = size // 16
    base_x = (size - base_width) // 2
    base_y = stand_y + stand_height
    
    draw.rectangle([base_x, base_y, base_x + base_width, base_y + base_height], 
                  fill=mic_color)
    
    # Recording indicator dot
    dot_size = size // 8
    dot_x = size - margin - dot_size
    dot_y = margin
    draw.ellipse([dot_x, dot_y, dot_x + dot_size, dot_y + dot_size], fill=indicator_color)
    
    # Save the image
    img.save(filename, 'PNG')
    print(f"Created {filename} ({size}x{size}) - Active state")

def main():
    """Create all required icon sizes"""
    # Create icons directory if it doesn't exist
    os.makedirs('icons', exist_ok=True)
    
    sizes = [16, 32, 48, 128]
    
    # Create regular icons
    for size in sizes:
        create_icon(size, f'icons/icon{size}.png')
    
    # Create active state icons
    for size in sizes:
        create_active_icon(size, f'icons/icon-active{size}.png')
    
    print("\nAll icons created successfully!")
    print("Note: These are placeholder icons. For production, consider using professional icon design.")

if __name__ == '__main__':
    try:
        main()
    except ImportError:
        print("PIL (Pillow) is required to create icons.")
        print("Install it with: pip install Pillow")
        print("\nAlternatively, you can create your own 16x16, 32x32, 48x48, and 128x128 PNG icons")
        print("and place them in the icons/ directory with the following names:")
        print("- icon16.png, icon32.png, icon48.png, icon128.png")
        print("- icon-active16.png, icon-active32.png, icon-active48.png, icon-active128.png")
