<!DOCTYPE html>
<html>
<head>
    <title>Create Extension Icons</title>
</head>
<body>
    <h2>Teams Transcriber Icon Generator</h2>
    <p>This page will generate the required icon files for the extension.</p>
    
    <canvas id="canvas" style="display: none;"></canvas>
    <div id="status">Generating icons...</div>
    <div id="download-links"></div>

    <script>
        function createIcon(size, isActive = false) {
            const canvas = document.getElementById('canvas');
            const ctx = canvas.getContext('2d');
            
            canvas.width = size;
            canvas.height = size;
            
            // Clear canvas
            ctx.clearRect(0, 0, size, size);
            
            // Background circle
            const bgColor = isActive ? '#107c10' : '#464775'; // Green for active, purple for normal
            const margin = size / 8;
            
            ctx.fillStyle = bgColor;
            ctx.beginPath();
            ctx.arc(size/2, size/2, size/2 - margin, 0, 2 * Math.PI);
            ctx.fill();
            
            // Microphone icon
            const micColor = '#ffffff';
            ctx.fillStyle = micColor;
            
            // Microphone body (rounded rectangle)
            const micWidth = size / 4;
            const micHeight = size / 3;
            const micX = (size - micWidth) / 2;
            const micY = (size - micHeight) / 2 - size / 8;
            const radius = micWidth / 4;
            
            ctx.beginPath();
            ctx.roundRect(micX, micY, micWidth, micHeight, radius);
            ctx.fill();
            
            // Microphone stand
            const standWidth = size / 12;
            const standHeight = size / 6;
            const standX = (size - standWidth) / 2;
            const standY = micY + micHeight;
            
            ctx.fillRect(standX, standY, standWidth, standHeight);
            
            // Base
            const baseWidth = size / 3;
            const baseHeight = size / 16;
            const baseX = (size - baseWidth) / 2;
            const baseY = standY + standHeight;
            
            ctx.fillRect(baseX, baseY, baseWidth, baseHeight);
            
            // Recording indicator for active state
            if (isActive) {
                ctx.fillStyle = '#ff0000';
                const dotSize = size / 8;
                const dotX = size - margin - dotSize;
                const dotY = margin;
                
                ctx.beginPath();
                ctx.arc(dotX + dotSize/2, dotY + dotSize/2, dotSize/2, 0, 2 * Math.PI);
                ctx.fill();
            }
            
            return canvas.toDataURL('image/png');
        }
        
        function downloadIcon(dataUrl, filename) {
            const link = document.createElement('a');
            link.download = filename;
            link.href = dataUrl;
            link.textContent = `Download ${filename}`;
            link.style.display = 'block';
            link.style.margin = '5px 0';
            
            document.getElementById('download-links').appendChild(link);
        }
        
        // Generate all required icons
        const sizes = [16, 32, 48, 128];
        
        sizes.forEach(size => {
            // Normal icons
            const normalIcon = createIcon(size, false);
            downloadIcon(normalIcon, `icon${size}.png`);
            
            // Active icons
            const activeIcon = createIcon(size, true);
            downloadIcon(activeIcon, `icon-active${size}.png`);
        });
        
        document.getElementById('status').textContent = 'Icons generated! Click the links below to download each icon file.';
        
        // Auto-download all icons
        setTimeout(() => {
            const links = document.querySelectorAll('#download-links a');
            links.forEach((link, index) => {
                setTimeout(() => {
                    link.click();
                }, index * 500); // Stagger downloads
            });
        }, 1000);
    </script>
</body>
</html>
