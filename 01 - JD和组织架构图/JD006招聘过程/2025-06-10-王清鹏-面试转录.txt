(剪輯)

王先生你好 王先生你好 打开这个体验我看一下 通过这个图纸的话,还有这些1.25.8.90这些标注的话 我个人理解的话,它是像比如6号的这个标注,用高强度的一些材料 然后还有5的这个,应该是这个2号,9号,5号这块是一个检测信号的发射器吧 然后Steel和826板桩是测量它的点燃东西之后的气体的发出的通道 然后其他的对这个都并不是特别的了解 您来简单的介绍一下这个工桩的目的和它的作用是什么 这个测试的一个过程大概是怎么样子的 我们这个工庄的话,应该是把咱们的生产出来的样件先固定在这个东西上面,就是一个这样的一个夹具吧,或者说是这样一个固定的装置, 然后通过相关的电子信号 或者怎么样的去触发它这个气体的产生 然后来主要的实验的判定的要求 条件应该是它从接收信号到这个气体产生的时间 这样的一个算法 或者说这个其他的都不是很 就是自己的一个推断吧 OK,好,谢谢 先签个名吧 那我们正式开始本次面试了 自我介绍一下,我是我们这边研发部实验室的负责人 姓史,我叫史亚伟 这位是我们研发部的总监,他姓刘 你好 这是我们人事的Justin,应该都知道了 那我们就正式开始 你这个路上都还顺利吧 还行 是怎么过来的 我从石家庄坐飞机过来的 然后到机场的时候 然后从机场坐5号线上1号线上过来的 好的 我们这里好装吗 就是导航 按导航走 就是路上地铁有点装潢 路上会让人下来一趟,换成下一趟的地铁 OK,OK,好 其他的都没有 刚来的话对宁波这个城市印象怎么样?

包括对我们公司,刚来的话 公司的话,最开始以为是,可能就是一个厂区就是我们的,对吧 但是现在看可能是因为合作啊,或者怎么样 就是和设计区各公用的相当于是,一层二层这样的 然后对公司倒是没有什么,因为也没有去过 主要因为我看过主要的这个岗位主要也是在线上嘛 然后也没有去过他们的实验室或者说是生产线上具体了解过其实 但是目前看起来的话还可以 然后宁波的话城市第一反应竟然是没有想象中那么热吧 就可能因为它是南方嘛 感觉可能要比我们那边要热点 比我们那边晚上啊或者早上啊都要两百以前 然后白天更泡夜 好的 上次我们经过线上面试的话 对你本身的整个过程印象还是比较深刻的 那么接下来我们就正式进入到一些问题 就是在我们的简历里面 你从一个职业的工程师进入到一个策划工程师 你能够从你之前的这些工作经历当中 跟我们讲一个你在转岗的时候 从一个执行层面到策划层面 你在转岗的时候觉得最棘手和不适应的案例是什么样子的 你当时是怎么想的又怎么去克服的 我们这边的话,大部分因为是要面对内部的客户,所以可能你在实验工程师这些段阶一类的经验,就是很有用在你策划方面,因为你需要对一些的我们那边的设计家,或者说是外部的客户,都是你在日常做实验的过程中,然后很会接触到,不是说是全盘就是交给策划一堆, 在实验过程中进行的一些问题确认 或者说一些计划的一些完成预期的反馈 都是可以直接和策划对接 就是不是分得特别的细 不是说客户策划我们 而是我们从前都是会有一定的交流 所以当时在转网的过程中 这样的一个过程中 遇到的工作上的问题 可能就是一些工作性质的一些改变了 一些执行啊,然后还有一些实验的一些确认啊,主要就是一些实际操作上面的一些确认和理解,然后到策划把统筹整个车型项目上的每一个实验的一些计划节点,一些实验的完成,一些实验过程的问题确认,当时这一步的话走的比较的困难一点,就是一下子从一个实施者变成一个 一个怎么转接吧,就是一个连接前边和后边的一个这样的指测 当时在一些计划的排布和一些问题确认方面做得并不是很好 因为当时实验室负责了座椅和一些平台外的一些像强度啊耐久力的一些相关的实验 但是到了测画那边你需要统筹整个的相关的 你都需要有一定的了解 你当时做实验不了解这个周期 你可能对人家实验员的一些计划周期就会产生质疑 或者怎么样 但是当时是根据 当初我做完第一半个月吧 然后做这个计划老是有一些偏差 就是有一些想当然的这样的一些安排 但是我自己在后期在跟踪 就是在意识到自己这方面不足 然后把我们实验室内部的项目相关实验的一些标准和一些他们实验里面相关的操作 都去跟踪学习了大概一个月的时间 大概了解他们是具体怎么样做这个实验 是怎样完成的 这个实验的中期的排库 大概有一个初的了解之后 对这个实验项目的排库就有一些的深入的了解 就这样慢慢转过来 比如说有一个项目,你对接的客户,包括我们内部的客户,质量部的,或者是我们的同一个部门的成名工程师,他要坚持增加一个非必要的,但是成本又非常昂贵的一个项目。 你的技术来判断 告诉你这是一个很浪费资源的测试项目 但是对方很坚持 你会怎么处理 像这种情况 一般也是有过我们这边的一些 之前也是有过这样的 但是我如果通过资深的角度来判断的话 首先是确认它这个 比如它要增加一个检测步骤 或者说要增加一个实验后的项目的判定 但是对整个的实验 我们初步识别的一个需求分析表里面 或者说怎样里面是不需要这一项的 但是就需要大量的周期和设备的调试 来完成这个任务 然后我们首先分析它提供的要求的必要性 就是不管因为客户提供了 我们就必须要进行我们的必要性分析 或者说你票过去这样肯定不可能 就需要进行有效性的一些分析 就比如我们 我看了那个 比如我们最近有一个华为的实验吧 做一个调节华为实验的一个问题 它这个问题当时是出现在低温的条件下 然后低温条件下 调节过程中出现那种吱吱的异响 就前段时间突然出现吱吱异响 然后实验员打配给我们之后 他们也是有视频调试的视频之后,出现一场视频之后,反馈给我们。 客户就要求,因为他要排查问题,因为我们这个时间先高温低温,然后再做常温, 然后我们是在高温做完之后,进行低温的时候,发现这个问题。 然后客户就要求说要问题复现 因为他们要确认这个具体可能是在哪个过程中出现的 然后他们就列出了很多种类的 就是试料性可能的方法让我们按照这个状态进行实验 就比如他们原本只需要做一个椅子的实验 考二维实验然后做完之后就出去报到 然后他们一下拉过来六个椅子 进行高低温的调试嘛 我们还需要按钮来调节 都需要搭建气缸 利用环境调节 而且这样会耽误别的环境实验周期 当时他提供了 六个方案 一个是从 就是还没有进行过任何实验 然后来进行 直接来进行低温的调节 就是先低温保持四个小时 然后调节就会有影响 第二个是高温 高温4个小时之后进行调节 但是这个问题我们之前在实验过程中 就这一条吧 高温实验这条我们之前在 实验过程中已经确认过了 因为我们是先高温再提温 然后再调温 每一次高温实验完成之后 就是高温比如100次 实验完之后我们都是要录制视频 来确认它这个高温实验后是没有问题的 然后他们就 可能我也不太了解 每一步都需要再重新确认,或者是怎么样的,就非要再加上这一条。 当时我们就通过我们在高温下加入的视频, 以及我们操作和设备的数据输出的一些电流曲线来发挥给客户, 然后取消掉了这一条,高温下之后,他要重新再试这个实验。 还有其他的四条分别就是,低温要多进行200次, 然后还有就是先进行常温再进行提温看情况这样的 然后最后就是通过两个方案吧 就是一个是提温后直接进行提温 还有一个就是重新进行但是加大了电压的一个调节 但是正常我们的实际的有一个标准是 但是他们不要进行了3.5V,就是未来确认一个问题嘛 最后我们经过这样的两个方案吧 确认了在12.6V下 12.6V下会出现吱吱的声音 增加到3.5V之后 吱吱的声音会在一瞬间出现之后就立刻取消 我们又增加到了15.0V之后 发现它是 就是它启动之后就直接没有声音了 就是我们会是忽略那个启动的声音吗 然后通过这个 然后他们那个因为我们的电机也是 供应商提供的嘛 然后当时他们就排除问题 就说可能是在低温调整下之后 它有结冰的一个状态 电机的齿轮各种方面 然后最开始电压比较低的话 它没有出现破冰的那个现象 所以那个齿轮在跟冰 这个动物中很难真实的想象 然后最后他们就增加扭曲 然后来解决这个问题 OK OK 好 就还接着个问题 这个是一个我们针对你的客户方 提出了一些测试需求 你们进行了一些交流 那么如果说你面对的客户比较强势 比如说是内部的 比如说是内部的 你有没有一个很清晰的,比较坚决的说不的经历 那你拒绝的理由有没有触及到一个什么 一个对你而言算是一个技术上的底线或者流程上的一个原则 你拒绝的对方,他的反应是什么样子的 你是怎么样面对对方的这种比较激烈的或者说不太友善的这种情绪 同时呢你又保证自己的原则不被突破 有没有这样的一个经历呢?

我的这种经历一般原则都被突破了 因为我虽然就是跟他们 我跟他们聊了之后 也跟他们说了不做这个东西的原因 也说了我们这个可能项目的困难的情况 但是总是他们就找领导 比如我们研发的领导就会找我们实验的大领导 我们实验的大领导往往就可能我说也不错吧 但是他就说既然他们都那样说了那么强硬了 你做作也没什么就这种感觉 OK你提前回答了我今天要追问的问题了 就是他有一些还好一点 就是可能比较能说得通 他就会自己跟领导反馈 但是有些他就是受不通上他就找领导 领导可能也一时不了解我们这个实验流程 他们就反过来再找我们的领导 然后我们的领导就会 就是我跟领导解释之后 他就会说哎呀 但是大领导都这样说了 他可能就是想要一个正常的一个结果吧 不能因为咱们事先有过这样的结果 但是领导都提出这样的要求了 他们就负责执行这样 那么这样的一个理由 这样的一个情况 你认为一般是技术上的底线被突破的多呢 还是流程上的底线被突破的多一点 一般来说是流程上的 因为技术上的我们一般都有一个详细的规划流程嘛 技术上不管是它是怎样出问题 我们怎样去验证 怎样来安排实验计划都是比较详细且清晰合理的 然后流程上被突破就可能多熬 我们那边实验室嘛 肯定是有加班的情况 就是多加班这样 OK OK,那么再换一个问题,就是说对于你而言,假如说还是刚刚这样的一个问题,你如果说有一种情况,或者有一天你特别坚持,你在技术上完全觉得这个是非常没有必要的,或者说你不认可对方的这个测试申请,这个测试需求, 你是 你会如何通过耐心的沟通来化解这个分歧的 你能不能分享一个这种 当然了也可以分享一个失败的案例 来解释一下 就是说对方无法被说服 而且这个情况又很紧急 你会采取哪些 哪些比较强硬的手段 或者说非常均的手段来说服对方 实验过程中是有过出现,因为他们有一些方法的验证啊 一些VVE的验证就是成本减少的这种验证方案经常会出现 直接的技术人员和我们实验室进行对接 而不是了解实验流程的一个人员来跟我们对接 他们往往不太懂这个实验 他可能就是自以为的理解 会在这个实验中出现受力的情况 所以说他之前是这样的一个状态通过实验 然后他现在这样一个状态 他就想验证一下是否能通过这个实验 所以在这个过程中 因为我们对实际的参数什么 并没有基础人员那么了解 我们只了解这个实验是否会验证到哪些部位 对哪些部位是有或多或少影响的 只要有一定的影响 我们都平时要去排查嘛 然后有一次吧 我们那有一个座椅的一个向后一个肩部压力的一个真人的一个实验吧 就是模拟前排座椅向后看的时候这样一个肩部对座椅的一个压迫的一个实验 然后这个实验是一个前排座椅的实验 因为你它的实际模拟功况就是 对啊这样 在行进过程中,往后来跟别人沟通,看这两边的后面发泡有没有,或者是褶皱啊,塌陷啊,在一定的3分钟。 然后这个人他就强硬地安排了,因为他是二排的一个后排的一个座位,他就要验证这样的,就是后面的切换。 正常的后面切换可以去验证一些震动或者载合的一些主要压力这样一个实验 但它非要做这个实验,就这样一个实验,因为它是后排的,没有这方面的功耗 这个还算比较好通过,就是我们把我们之前做过实验的一些实验报告,还有一些视频 还有我们的实验标准中详细的一些,比如说,实验中写出了 试用工况是前排座椅向后扭转是对肩膀肩部肌肉的一个压迫 然后试用前排座椅这样的一些标准标准的话 是详细列出来的一些标准标准,确实是这样 然后他当时就特别的倔,就是说 那如果我是想,后面的人想去后面看看这样的 可能说你跟他讲了,但是他就是要坚持自己的想法。 我们前排后排这样的次数肯定也不一样。 我们当时就给他提供两种解方,一种就是实际的,这个确实没必要做,他当时也勉强不想接受。 然后第二种就是,比如我们现在可以给他根据他要求的这样一个实验状况来进行实验,就是比如他是后排的,我们可以适量的减少次数,因为他毕竟不像前排一样次数那么多,我们正常就是前排比如是一万次,后排就是五千吧,这样的一个次数的改变,就这样,这样这样,次数改变一下。 然后因为它是后排的,所以它没有 它中间座椅是这样的嘛,没有 比如左侧它没有右边这样的一个 对吧,然后它只有左边的地方,然后我们当时就跟大家反馈 如果你去做左边的这个实验的话,可能实验后面有点显变啊 这种情况你是认为它是合格还是不合格呢 然后就通过这样的一些实验标准的解读给大家 OK,OK,我懂你的意思了 那我来重述一下我刚刚提的这个问题啊 我想说的是,通过你刚刚的介绍和交流,你比较擅长的应该是这种耐心的沟通和交流 然后双方达成一些妥协或者一致,好推进这个实验项目进行下去 但是当这种耐心的交流和沟通失效的时候,你有没有这种经历 或者说你想象一下,假如这种耐心的沟通分歧没有办法进行化解 有没有这样的一个案例,而且对方你也无法说服他,你会采取哪些更强硬的或者非常规的手段来推进这个工作?

我们这边的话,如果遇见这种,比如我可能觉得他没必要, 就是我们像这种大的分歧一般就会出现在我们实验的第一个步骤吧,就是需求分析这方面。 可能这个新,刚提供的一个实验需求,我们经过分析之后觉得有一些是很不合理,很没有必要,或者不相应的那种情况,就是还要坚持要做的。 但是像外部客户的这种需求,我们一般来说是耐心些,但是内部客户的有时候一些没法沟通的一些要求的话, 我们就是会,可能我们也是人为研究或者怎么样,就是把这个东西的Language Map总结之后会向我们的支出领导做一个汇报,然后通过支出领导他们去沟通解决这样的一个问题。 像我们一般采取的手段也就是这样 因为我们个人没有权限那么的大吧 或者怎么样 就是很多事情需要领导去沟通这样解决这样 OK 行 那下一个问题就是 我补充一下 这个因为你刚才讲是工作当中 其实看来有很少去拒绝在工作方面的事情 那个人生活当中有什么拒绝别人的需求或者需求的吗 个人生活中倒还,我其实也算是不太拒绝别人,但是有些问题,有些事情,比如有些人让我帮他放假,就是我们可能需要值班嘛,也不能算是工作,就是可能需要帮忙去公司值班一下,也算是一个事情吧,就是别人让你帮助的事情。 就是可能需要考虑,就是如果我这个人 别人如果我确实当时没有什么提前的安排,没有什么提前的计划 可能就不太会去,但是有时候如果只要有自己的安排或者说自己的计划的话 就不管他是怎么样的事,或者说就是个人生活上的姿势的话 只要计划好的就不太会去改变自己的计划 如果就是没有 诶,比如我当天就是前周没想好这周干什么,就可能就在家休息啊或者怎么样 您是来找到我的话不会拒绝 但是如果说是提前就安排好这周 我要有自己的事情要做 那不管是领导还是怎么样 就是尽可能领导的太过那个的安排 就是很高级很高级的那种安排 那肯定还是得安排 但是如果就是普通人的一些 让你帮忙搬家呀 那种帮忙去接一下车或者怎么样的这种事情 OK,好,理解了 那我们换一个层面的问题 假如今天你是我,或者说你是你现在公司的领导 或者说你们实验室的负责人 所有的团队或者组织,它的运行都是要体系化的 或者说要以系统的方式来运行这个团队,对吧?

对 一个任务就是要投入资源 就是说第一你要引用一套虚拟的仿真系统 来减少相关的物理测试 这样的话呢 投入的成本比较高 但是很多测试就可以通过这个仿真的系统 就直接可以获得一个结果了 而且节约了很多的其他的成本 但是前期的投入可能会比较高 这是第一个 第二个的话呢,你优化现有的测试流程 每个测试的流程的话呢,每个流程都可以把它的效率提高15% 你来跟我说一下,你会优先倾向于投资哪一个项目 可以从你觉得性价比,比如说投入和产出,这个投入产出比 技术层面的,还有团体能力建设,以及长期的一个整个系统层面的运营,你来给我们介绍一下你的想法。 如果没有听明白的,我可以再把刚刚的问题给你再说一下。 主要是分两种的,一种是减少现在的实验流程的15%优化,一种是引用技术层面来减少一些不必要的实验项目。 它是引用一个仿真系统,通过模拟来获得一个可靠的实验结果。 对,我们那边也有一些座椅的强度实验,在进行真正的整车,DVPA的实验的结果之前,都是要进行放置的。 因为这样的话可以具体得出,就是通过我们公司吧,我可能更倾向于技术层面的一些升级,改善这样的。 因为它对我们而言不仅仅是开源的一个措施吧 如果减少实验项目的一些必要性,优化15%算是坚决的话 那个技术层面其实可以更多来说 虽然说前期的投入可能会比较大 但是它其实起到了一个开源的效果 因为我们不管是做强度类的实验还是做耐久类的实验 比如一个碰撞的实验,它都是需要你输出当时整车的一个碰撞的加速曲线,然后你通过仿真来计算出你在座椅在这个碰撞峰值收到的这个最大的力值吗?

然后你通过这个例子来选择你作业设计的相关材料,然后相关的一些使用的东西,都是可以通过这个仿真来得出一个基础的相关的有效性的一些数据,得到一些这种更有效的数据。 通过这些数据就可以 因为如果没有仿真这个技术的话 可能你之前都是凭借之前的经验 比如你新开发出来的一款作品 你这个结构强度你可能都是通过 一些设计经验来预估的 但是如果有仿真系统的话 虽然仿真这个过程可能会比较慢 而且每次仿真成本好像 还确实挺高 所以但是 我们倾向于这种比例或者说是这种铲除比这种的 具体计算的问题 OK你注意我刚刚说的一个前提 假如你今天是我或者说你是实验室的负责人 你站在管理者的角度 不仅仅是投入铲除比 你还有没有其他可以参考的思考的层面 来分析这两点的选项 懂我意思吗 比如说 比如说我们团队的成长 我们的培训,我们其他层面的一些,等等 如果在管理者角度上的话,我们新开发这样一个系统 首先肯定是需要软件这种使用权 各种的,我个人,可能我自己理解也有偏差 可能这个软件使用权需要购买,相关人才需要挖掘 就是可能你当前部门内没有相关的人才 因为你没有相关职业岗位,你肯定要在体系内增加相关岗位,这个岗位你还要经过体系认可来确保它有效,然后后续招。 但是经过这样的 如果有相关的能力 因为我们可能 我们站在实验室的角度上 每多一项实验 可能都是对自己 比如因为我们是内部的 如果我们只要有了这样的能力 我们就可以减少委外的费用 然后就对外部的费用也可以减少 肯定也算是成本对吧 成本也是一个一定的减少 OK 没事 这个稍微有点那个 OK 接着呢 下一个问题 就是说我们实验室的话 整体运行的话是比较正规的 通过我对你的之前的一些交流和了解 我只是猜测 你可能不太偏好文件化的一些工作 那么你是怎么理解这些 打个比方 整个文件性的东西 记录性的东西 体系文件化的这些东西 可以称作繁文缛节性的这些东西 在我们的整个组织的运行当中 这些繁文缛节 包括质量管控和安全管控 就是说这些文件 你是怎么看待它们的必要性呢 过往的处理不合格品的经验 你谈一谈 如果说我刚刚提到的这些文件化的东西 如果缺失了 或者说不比较薄弱 可能会导致的哪些比较糟糕的后果 有没有类似的比较经历过的经验 自己经手的可以跟我们分享一下 我们那边的话也是一个体系化的现实 它每年都需要经过CNAS体系的内审 或者外审 就是由CNAS机构的人过来 然后来审核我们整个实验室的体系 然后我们 1935年 就是可能我们整个部门都存在了一个 忽视性的问题 然后被当时的内审员查的外审员查到了 然后从那之后我们就更加重视了一个质量的一个 管控吧 就是对您说的那个 相关体系文件的一个 就是很简单一个 点击表的 识别号更换的一个问题 然后当时我们的 最开始 过道的时候我们的那个 体系的识别号是 A-0 当时他们要更换这个识别号的 原因具体是因为 我们经历了一次 整体的一个内审 就是过审的项目 然后当时需要把这些过审的项目的点验表和不过审的时间号要区分开来 就是那些过审的要从1-0变为1-1 然后那些之前的就还是1-0 然后当时我们的指向体系专员 可能算是体系专员这样子 就在群里发了一个公告 机器意识不太强,就比较薄弱 可能都没有注意到这个相关的文件 就是它这个文件当时 可能大家最后内部外传发现之后 可能也觉得不是什么大问题 但是就是通过这个小问题 你可以就是你识别号都没有对得上 然后你却归档在一个文件里面 那你对你之后的一些不良产品的一些记录 一些文件类的,尤其是这种不合格的报告是要着重留存的,因为可能客户都需要,之后可能会再次需要,我们都需要打印留存。 然后,如果你最简单的都没有做好的话,他们是没有办法确认你对一些重要文件的一些保管的一些有效性。 我当时就提出了这个问题,算是一个很大的问题,查了我们近半年来所有的一些不合格的报告的一些识别号的问题,确认,查了好久,但是当时就还是,解决完这个问题,因为出具一些政改方案之后,大概延期了两个月吧才通过了,所以就感觉这个无限很重要。 OK,那你在你的工作当中面对这些文件 在这个审核期间你是怎么看待这些大量的文件性的东西的 包括你可能自己会做到编制了一些文件化的东西 或者别人要求你进行的填写的或者编制的一些文件 你要遵守的一些文件等等这样 其实我们繁文缛节的话倒也不算太算了,因为它是体系运行的一个必要场合,它需要一级一级的确认,一个一个问题的确认,所以不可避免的肯定会出现这样一些文件的出现。 但是我做的另一边的方法就是 我们那个电脑桌面上 就是做好每个文件的分类 就比如我们当时在做实验的过程中 其实涉及到的文件并不是很多 就是一些原始记录 就是因为你的实验过程中要进行记录 每个实验都需要有纸质版的记录 来确认你进行的环境啊 实验操作 然后还有设备的一些参数的确认 每一个实验你都要有这样的一个原件记录的确认 当时我们的记录就是做完之后 纸质版的封存 然后打印就是扫描版的自己留存在一个文件里面 当时我也觉得特别的麻烦 就比如因为一天不可能第一天只做一个实验 一天有好多的设备 你都需要去进行实验原件记录的填写 想着要么就只留原件就行了 要么就光扫描怎么样 只留原件 当时最后是有一个问题,就是一个失效性的分析,就是一个座椅在实验过程中出现问题了,然后在售后过程中又出现了相似的问题。 就是我们去年24年下午,10月份吧,我们的坦克的车型出现了一个后面折轴的问题嘛。 然后当时就查询这个实验当时的不合格的降万纪录和合格之后诊感措施的一个降万纪录, 然后就顺序去查到当时的一个原人记录的一个实验参数的设置, 还有那个实验曲线的确认,当时就顺序查到这些东西, 然后当时就发现了材料的缺失, 然后就没办法确认当时的一个状态的 就是你可能觉得你每次都是这样设置的 但是他们需要一个这样的证据来证明当时确认是这样 OK,我懂你的意思了 下一个问题 就 你现在负责的这个项目 有个项目 现在出现了一个紧急的 缺失的一个资源 比如说你的温度箱 可能需要一个配件 但是你没有 你没有这款设备或者说 这就是说平时的供应商对应的这一块的设备维护保养的权限是不在你这里进行维护的 你只是进行策划或者说收到这个实验结果 好吧 这是前提 你是怎么样在升级到你的领导这么一个前提下去把这个问题解决了 问题回到正轨 一般这种情况可能就是 实验员的环境机箱出现了 问题就是需要修复 然后这个设备的一些配件的一些提报 或者说是售后问题 是需要另外的维护人员来进行 但是我们这个实验也比较着急 需要在有限的时间内及时修复完成 并且满足实验的节点 这种情况我们一般不会先通知领导 我们会先分析异常的问题和解决问题所需要的时间 可能我说的是一些解决的措施吧 您说的怎样影响或者怎么样 温度箱或者说这个设备的维修需要找供应商来紧急的提供这个备件 但是呢你是这个事情的负责人 你比较着急 你想尽快的推动这个事情尽快展开下去 那你又不想去升级到领导这边 但是你又没有和供应商交流的或者说以往的经历或者权限 你是怎么解决这个问题 整个实验进度恢复到一个正轨 这个我的经验可能不太适合 但是我们不是通过我去找 但是是通过我找了 就是这个环境下之前使用人的相关信息 然后我们这边是有一个设备的维护保养人的信息 前使用人员之后 拿到了这个供应商 就是我们这个环境效盛展商的这个 OK 就是说找到这个温度箱 维护保养人员 让他去和供应商交流 是这个意思是吧 对 OK 好的 然后这样 当时是解决了 但是然后也没有出现您说的 更复杂的情况 OK OK 嗯 从你 从你过往的一个经工作经验 你是从一个实验工程师转为到实验策划 那么你接触到了比较多的一些管理性质的工作 可以这么理解对吧 因为涉及到大量的策划性的东西 要去协调交流和沟通 在你看来一个优秀的技术型的项目负责人 最容易掉进的陷阱是什么 你认为你目前在哪些方面还需要一个提升 才能避开这个陷阱呢 我认为技术型,就我自己吧,从算是技术型的人转为一个技术型管理的工作经理吧,我认为他最容易掉的漏洞就是,因为他从技术型,他不是一个全面的技术型的人,就是所有的东西都会吧,或者怎么样,所以他可能只获得自己的一部分, 然后他就会想当然的类比 就是可能会把自己 可能你之前做的很好 然后你让别人接手完这个岗位之后 你默认的他必须要按照你之前留下的一个工作经验 就按照你自己的工作经验 可以说是比如说你需要操作这个机器人 你可能一个小时或者怎么样就完成这个东西 或者说现在的人员的一些工作的情况 它实际情况并不是你想象中的 你可能根据以往的经验而做出错误的判断 然后进行错误的计划周期排布 然后或者说就是把一个项目的节点 你可能自以为是的调整为三天 但实际在目前的状况下 它是需要稍微更久一点时间 可能觉得这个东西 就是我是在经历过一些错误之后 我才反映过一些东西的必要性 并不是由你一个人来做出确定的 因为你的经验可能并不是那么的足 而且需要多方面的去分析 就是基本上可能就比较相信自己 但是做管理型的话 可能需要多方沟通 多方权衡来确认这个项目的合理性这样 OK 所以技术性的相处的人 你的理解是因为做技术 他就比较自信 他比较相信自己的技术 对整个这一些的理解 他当他转为管理人员的时候 他可能会过于自信 就比较盲目去推进这个管理项目是这么意思 对吧 我个人是这样的感觉 然后需要改进的点 可能就是现在就是因为对管理项目 管理的一个相关的流程的数据化之后就不再那么近 就是可能会在一些问题上给出自己的判断 但全部会是一无形这样的 就需要多方确认之后 比如有一些实验可能没有按照标准实验 然后觉得可以 但是事后他们会问你为什么 然后所以现在的话就要出去一个会议机要多方会见之后再进行 这样的话 让我们稍微调转一下方向 现在你面对一个技术型的管理负责人 顺便的领导 假如说是我 我可能对这个项目或者说对这个技术 自认为比你了解更多 但是你可能站在管理或者策划的角度 你看待这个问题的角度稍有不同 那你是怎么 你会怎么样和技术型的项目负责人 进行交流和沟通的 我可能先从两方面,因为技术层面他都了解,所以我们可以通过他现在已经了解的相关情况,然后确实他说的是对的,但是只是从可能实际实施和理想情况是有差别的,我们如果是我的话,我们会把这个问题拆开来细分来回报给他。 然后他可能会提出各种其他的问题 然后再引以为战回答这样的 OK 好 下个问题就是说上一次的话呢 我们在交流的时候你也提到了就是你家里人都在舱 那个太舱嘛 那你离家近是你换工作的其中那一个比较重要的原因 好吧 这是一个背景 三年之后,你希望成为一个哪方面的专家或者说管理者呢?

为了实现这个目标,你觉得你当前最需要提升的能力是什么?

最需要我感觉有两点吧,就比如我们加入了这个工作的话 第一方面就是一个对咱们的公司产品的一个了解 这是最需要的,因为我对它了解并不是充分,因为之前也没有接触过相关的东西 然后第二就是英语方面吧,有可能一个是技术知识,一个是语言知识 这两个方面也是最需要提高的 你还没回答,就是说三年之后你对自己的一个规划和期待期望是什么样子的 三年之后的一个规划吧,因为之前上一个面试就说到了,我可能会用到三到五个月之间来熟悉我们的工作内容。 熟悉完工作内容之后,我可能通过网上的一些信息啊,或者说咱们公司的一些前辈啊,具体是怎样进行这个工作。 通过我的工作可能会接触到的一些技术方面 或者说是一些实际公益过程中的一个各个问题解决 这样的 打断你一下,不好意思 我来再解释一下 不是说你是怎么样在三年内或者说三个月内迅速的适应这个团队 我说的是三年以后 你觉得你会成为什么样的一个 技术型的,管理型的等等都行 你觉得你对自己三年之后的期待是什么样子的?

或者说,现在,三年以后的你,你想跟自己说什么?

三年以后的自己 你觉得三年以后你成为了一个什么样的人?

什么样的一个职业生涯?有什么变化?

如果按照目前的进度下去的话,三星或者自己应该是能够掌握公司的基本流程,能够说一些熟练的英语,然后能够在客户功能中有更好的交流。 对技术层面的话,希望自己当时能够掌握我们公司产品的工艺流程,对工艺生产的技术方面有更多的掌握,需要这样的一个情况。 假如说你现在是一个老员工,你已经在这里干了最起码三年了,现在我要重新招募一个刚毕业的大学生,一个新人。 就像两年前你刚参加这家公司一样 诺博 你会用什么样的方式来帮他快速成长 你觉得你的管理风格会是什么样子的 如果说你会想一些方法 措施来帮助这个新人快速成长 你来举三个例子 哪三种方法 就比如我现在招来一个新人 我们要做一个交接,然后需要在短时间内进行,让他有一个上手的能力 然后我可能会把自己的工作的主要性质,可能这场工作中主要是要拆分出来 要通过不同的阶段来跟大家讲主要的工作内容 第一个阶段可能就是让他了解自己的工作内容 因为他如果是一个刚毕业的学生,没有相当经验的话 确实是不太好上手,我们实验工程师倒也好,策划工程师肯定是不太好上手 然后如果就是让他很快地去接受的话 我们就一般有,如果做我自己的话有三个要求吧 一个就是我们对我们实验室的整体的实验项目的一个技术的了解 这是作为策划的一个必要条件吧,可能就是我认为的 至少要了解它的一个排名标准 你可能不需要知道这个东西是怎么做的 或者单纯你需要知道它这个东西做完之后的一个正常的情况应该是什么 而不是说是一些异常的情况 这样来的话会帮你确认或者说问题的根茎 这样的话需要防止 这是一个最重要的一个成员 然后第二就是我们的一些流程化的一些管理吧 这样的一个过程要详细地去分析去告诉他 第三个可能是一些过程中的问题 或者说我们之前所有前辈们可能出现过的问题 我们可以通过经验的方式去告诉他 让他来减少在过程中可能会出现的错误 OK 那如果你作为管理者 你觉得你的管理风格会是什么样的 或者说你现在下面有带人吗?

有一个,我们也不算是管理,就是一个新人,然后我带他去。 如果说你将来带团队了,你觉得你的管理风格是什么样子的?

这个问题,对我自己的分析的话,我可能不太适合, 可能因为之前自己从来没有想过去当领导或者管理人这样的 就是管理项目可能还好一点 因为你自己是平级的 就是管理的话可能并不是太好 我对自己的一个了解可能 没事 假如说你就是坐在这个管理岗上 已经最起码三年了 你觉得你的风格是什么样子的 比较好说话,比较严厉,比较怎么样子的,你可以随便说一下 如果我是从一个基层员工做到,而不是一下子到万里当 正常也都是从现有基层经验嘛 就比如我们现在的这个直属领导,一个我们的科长 如果我现在是我们科长的话,我可能先从自己的资深经验出发 就不会是那种特别严格的,但是对一些关键性的问题输入的一个确认可能要把控的比较严格。 就可能我们这个实验工程师,你其实最重要的输出物就是你的提供给委托方的一个实验报告。 对关键的东西的质量意识要好,但是对日常的一些细致管理可能会不做太多的精细化要求。 好,下个问题的话呢,就是关于这个加班 我们这个团队的话呢,平时加班不多,但是这个加班嘛也是难免的 有些项目关键的时间节点的话呢,还是,加班是难以避免的哈 这个应该能理解 那你对这个工作的强度啊,还有你自身的一个生活 有什么预期吗?

我来的时候,面试之前,我也不太了解外企的工作情况 可能我之前一直找的都是主机厂 但是咱们现在算是供应商这样的一个角色 我们之前也是在做供应商 这种情况算是比较能够理解,确实是没有办法,因为咱们是模组商而不是主机商,人家接点接点,如果你因为问题,确实肯定是一个问题,没有办法。 所以我可能对加拿大,如果因为自己的问题导致的一个项目接点可能的一个 就可能延期的这样一个可能性,我肯定会在自己能力范围内先领先解决好公司的这样一个项目问题 是这样的 好的 下一个问题就是说,你比较喜欢什么样的领导,还有一个团队氛围 然后反过来讲的话,或者说你比较反感什么样的领导和团队?

比较喜欢,可能就是沟通比较轻松一点,然后可能就是我对领导肯定是需要有一定的要求,而不是说是特别的什么样的都是随意那样的, 但是所以我希望您告诉我们的是 可能在一些方面 一些比较细致末节的小事 一些 呃 可能你平常上班的时候吃个零食啊这样的 就是可能说是有时候 可能有时候你在实验室可能不能这样 你可以在工位的时候给你这样的一把 你可以这种这种小事可能不好管 我给你讲一下 要对 ok 要对那个我们关键输出物的一个质量的问题 要有一个自己的把控 好的,行 OK,我大概就先这样,请 在我们Rocket实习里问的比较多,有些问题确实也比较因人而异的 所以他没有标准答案,我理解,但是主要是看你的个人的一个分析 那我有几个点再简单的问一下 我看你这是23岁,你是哪年出生的?

2001年 几月份啊?

零一年八月 八月 那现在应该是二十四左右是吧 对 马上就二十四岁了 快二十多岁了 好 这是一个 第二个我看你这个手上带了一个 右手上带了一个环 这个是有什么特别的寓意吗 女朋友的 啊 女朋友的一个皮筋 就平常就戴着 女朋友的标记 这是有女朋友的标志啊现在 可能就是现在都游行这个嘛 OK OK 这个我这个人老了 所以我问一下有什么特别的意义没有 OK,很好。 第三个问题是,我刚刚看了一下你回答的这个点,你可能还没看。 你自认为答的如何,这份石井里出的一个书面的卷子?

我觉得有一些问题还算是我的一个经常接触的比较多的吧。 只能说是自己的一个理解,可能也许还有一些没有觉察到的地方。 需要改进,然后公图的题确实是不太懂,是需要以后去提升改善的一个地方。 OK,如果在你图那个先不说,你已经写了书面的东西,而且我们实际已经标了分数了,你自认为你能拿到多少?你感觉一下。 结合分吧,我感觉应该也是差不多,因为我感觉写的都应该写的比较的笼统吧,也没有自己个人的一个风格这样的,就是一些比较流畅化的一些知识,就比较正常的一个感觉吧。 那个图子嘛,刚才我过来看到你说了一下,确实是不对,就是我们那个图子不是这个意思 好的 好的,这个需要去再完善一下 如果我们录取你,你大概多久能够报到 可能的话是最早的话 如果因为我目前的话可能家里面那块 7月中旬的时候有个比较重要的事情 原计划就是可能我在之前公司的话 可能就会清一下家这样子 一个大概时间左右的一个长假 因为就是家里面的一些问题嘛 就可能想在7月20号左右 就是大概一个月的时间生活就不止 因为当时那个事情大概办完是7月17 18号左右 OK 所以大概是8月中下去入职是吧 8月初就可以了 OK 好的 然后英语的话因为上次我们简单教了过嘛 但是不知道今天你准备的是啥 今天我就知道要考试就一直在找一些别的方案 就没有准备英语的自我介绍 就是你填的那个考试的表是吧 嗯 那你准备的其他东西是吧 嗯 你是02年对吧刚说的 01年 然后还有一个问题就是说,毕竟你是有大概有三年四年的左右工作经验了,现在是三年了,如果让你给我们的部门啊,就是因为是两方面的啊,一个是你过来的话,你要熟悉我们的产品,熟悉我们的流程,熟悉我们的人员啊,那么另外一方面的反向来讲的话呢,你觉得你有哪些,你觉得有哪些,你觉得有哪些,你觉得有哪些,你觉得有哪些,你觉得有哪些,你觉得有哪 你感觉还是比较好的点,其实可以带给我们新的团队呢?或者带给我们新的人员呢?

个人的话,可能一方面年龄比较小,可能有一些精力比较好一点,对事物的接触性,可能对共同,就是可能对同事间的沟通也是还是比较向往的,可能就是新同事新环境的一个向往的这样的一个分类吧。 能够活跃一下团队的工作氛围 也希望自己能够做出让同事们都认可的一些 不让他们看不懂的一个要求 有什么具体的措施可以活跃我们的团队氛围吗?

或者有什么想法?

你感觉 团队氛围 我主要也不太了解男队 所以可能到时候需要 因定制宜一下 或者怎么样 你有这个意愿是吗?

有这个意愿可以去活跃一下团队氛围 对,因为工作的时候 如果太过沉沦的话,也不会这样往前走 专业的东西我就不问你了 因为我们上一次面试,包括今天我们实际面试,问的比较多 所以我就不再问你专业方面的东西了 平时爱好什么?

平时我就是打打羽毛球踢足球 你喜欢踢足球吗 踢 我是踢右边 那你说你女朋友呢在哪 在保定 对也是在保定 但是她因为刚入职嘛 她就是比我晚去了几天 然后她现在目前觉得自己的 可能也是找工作之后不太顺利 就是这样的 同一个公司 嗯 但是不一样的行为 那还有就是 后面问题 那3到5年计划 我们都是计划要来南方这边 因为他是这边 他是那个武汉的人 他也是南方的 然后我们这个车企啊 我们因为我们 因为我们要求基础的目的吧 最不足的目的就是涨工资嘛 沿海这边发展比较好 工资也比较高一点这样 所以他肯定到时候会是来 他原本是面试一个上海的岗位 想去但是是一个外包的那个嘛 然后又考虑考虑就先不过来 就还是在那待三年嘛 在上海市 然后再回来这边再找一个工作 宁波他那个基地 当时也有他时候的岗位,但是经验太少了,可能后续经验充足了之后可能会来这边试一下。 他是什么岗位的?

做NVH的。 噢,噢,噢,噢,噢,噢,噢,噢,噢,噢,噢,� 但是可能需要经验比较多一点 然后他现在很简单 就是刚一年多一点就要 把他寄回来那里 他隔壁托福那边去找挺多人 我这是个问题 这是一个问题 上次我们面试的时候聊到这个 没有问这个 对对 我好像没 写完的我没问 OK 我稍微补充一个问题啊 不好意思 你高考的话考多少分 在河南当时是560 考了560 你是理科吧对吧 你们这个学校这个大学是几百 一百 好继续 河南那边 种村多少 750吧 都是 应该也全国院吧 对 全国一年 没有概念 反正河南那边竞争压力挺大的 人太多了 OK 然后你的性格测试嘛 你做了两遍嘛 因为两遍会有一个偏差 但是我默认还是按照你第一遍做的时候 是个自信的探险家来认吧 反正我个人认为 你结论是什么 自信的探险家 自信的探险家 是个I人 但是I的体现不是很理解 眼内瞎那种 自信的看谁 其实我也不知道为什么会测试还是个爱人 但是其实别人对我的评价好像 就是我说 我跟他们说做这个评价和测试之后 说是什么爱什么的 他说你是爱人 然后就感觉 所以说我又仔细查了这个 爱和异代表的一个意思嘛 可能说爱的意思就是 可能是向内寻求一个 一个 然后异就是向外寻求 在里头就喜欢出出 还是去跟别人玩 对 可能是这样 但是这也只是一部分迹象 嗯 说到这个的话 你平时除了运动这个爱好 还有那个 这个也不能天天 你是天天去吗 运动吗 不是不是 打游戏吗 对 打游戏 平时的读书 玩那个 游戏打的倒不是挺多 我们当时 我们 我平常 因为我们可能区域时间比较多嘛 我们可能休息时间一般两半个月会去我们公司的人会去踢个球什么的 然后有一些穿修的休息时间一般的旅游的方式 要么就去保庭那边的一个狼牙山还有月牙湖那些地方 露营一样的 因为我当时我不是把自驾车开过去了吗 然后有个那个我买了一些东西可以在那边玩玩 打麻将啊这些啊一些娱乐的方式吧 你一个人的时候都干嘛的 一个人一个人的时候我比较一个人的时候喜欢看小说 就一些小说之类的 然后或者就是玩一些 Switch上的游戏这样 你看啥书啊 什么小说 看的都是一些 一些 玄幻类的 不光是那些 主要是一些 最喜欢看的是一些历史类的 一些历史穿越类的 还有一些历史讲义类的 不是全话的 喜欢看一些穿越老明朝改变历史啊 这种小朋友 问你这个问题的话呢 是你过来了之后新环境 你可能也暂时交不上什么朋友 你得有一些兴趣爱好应对这些时间 这个意思 这个倒还 我对自己这个还是挺那个的 挺自信的嘛 因为没谈女朋友之前 然后也是在这边工作嘛 当时谈女朋友是谈一年多了 然后当时在一年多之前 将近两年的一个时间 就最开始 人生最开始那段 肯定也是没有什么认识 就是后来慢慢慢慢通过兴趣爱好 或者怎么样 这时候公司同事开始出去玩了 之前一直都是有一段民俗的这样经历 没事 我们部门的话 现在跟你年龄差不多大的小年轻也有及格 你应该到时候不会太无聊 OK 谢谢 没什么别的了 你有什么想了解我们公司的吗 感觉特别的,因为我是先坐到大气站,大气站或者大树过来,感觉公司地方可能比较偏僻一点,就是这样的感觉。 你们平常是住在哪儿?也是在日本吗?还是开车过来上班呀?

我们这个环,这地方算不上偏,因为我们这里是属于北俄区,北俄区呢是在英国的, 你可以理解是脊脚干,宁波的脊脚干 对对对 宁波的脊脚干 但是北纬它又是一个相对于比较繁华独立的一个区 因为它跟宁波中间也有山和河 基本上很少会互通 那我们这边离北纬的主城区大概八公里 骑个电驴可能十五公里就到北纬的城市了 所以这个地方你说很偏吧,也算不上很偏 但它正好夹在宁波和北纬中间 就是城南牛湖 也是这样的 这儿的话呢 是一个工业园区 我们是保税区 保税南区和保税北区 我们这是南区 然后这边过了保税南区 那个门口的马路对面就是一个比较热闹的一个广场 那个我们刚刚路上看见 对对 原本是在村里面 然后喷一下 然后又进城了 然后过了那个地方 就是你下车的那个站点大气 再往那边去 就是快接近城区了 也很方便 其实离地铁口也不太远 因为他说公司那个宿舍好像也不太远 所以说离地铁口可能也不太远 也算是一个闹钟取静的一个位置吧 你如果将来感兴趣 如果去北伦去玩 去宁波市区地铁一号线 然后去 如果再去别的地方旅游 周山 我那边去就是周山 我那边去西口这些景区也都很方便 我去温哥博的时候 然后就路过了一个景区 就感觉是一个什么潭 不知道说是一个张家潭还是一个景区 就排着足迹在那 感觉环境特别的好 比我们之前在那个环境好多了 潭在哪 不是不是 张家潭 张家潭嘛 就在机场旁边 那还 丽舍机场 那还挺远的 机场 刚好在对角线那边 机场跟我们这两个极端 对 所以我当时坐地鞋在一号线还被叫下来两次 在说什么 我就在那坐着坐着 我在那看着东西 他转身了 坐到短线了 然后就被叫下来了 然后上去就坐一站又被叫下来了 宁波的话呢 相对而言 包括我们北轮整个人文 包括整个城市的管理都比较人性化 然后很多的方方面面 包括政务都还是挺不错的 跟北方比起来的话 还是有一点点比较明显的 不一样的 对将来会发现的 这个在网上就感觉到有很多区别 而且你在路上确实感觉不太一样 就是怎么说呢 就可能你可能去到我们那边 你就知道了 路都特别的好 不好不好 而且好多 你说你在市区走一圈 就一条路上 可能就会有好多修复的 但是我这场时间没有看到 所以查询越车越好 我们宿舍离这边蛮近的 就是你门口海关门口开了两个小时 走路就十几十五分钟到十八分钟 基本上这样子 一公里 到时候今天可以去抽充吗 宿舍 啊 我看我晚上需要好像实验的丙级 那如果有时间的话 到处去逛一逛 等一下我也可以 要不要去实验室车间 你要去逛一下吗 嗯 也行 可以 好 其他的话呢 就是说 我再问最后一个问题 你是这个岗位的话呢 我们目前的定位的话是一个相对而言协调 沟通交流可能会比较多的 我的期望是这个人他也是要从这些一线基础的东西做起 也就是刚刚我问你的所谓的技术负责人的那个陷阱 那个陷阱这个问题一样的道理 就是说这个人的话呢 进来的时候他要把所有的项目都要一一的做齐 要不要怕脏 不要怕苦 不要怕累 他要把这里面的一些细节他都要抓住 吃透了 把这个工艺 把标准 把质量 把安全都融会于心 在这个基础上,它才好去做很多的协调交流和沟通, 因为我们毕竟是技术研发部门,它并不是工厂运营团队下的实验室。 那么你沟通的交流的对象会充斥面对的是大量的技术人员, 所以你要懂一些技术,这样的话我们才能够有交流的基础和前提。 那么在这个前提下的话呢,你做项目负责人交流,任何的一个过程,它无非就是人机了法还,对吧?

那么,比如说现在你要买的一个东西,一个设备,它出现了一个问题, 那你要去找不管是供应商也好 还是我们内部的团队领导审批卡在哪个环节了也好 或者说是财务付款也好 你需要和别人进行一些大量的交流 对吧 这是我们工作当中的设备采购环节等等这些的 当问题卡在了某个环节的时候 你去和别人交流 但是别人不配合你 对吧?

对,那S会出现 反复的交流不配合你 可能会面对这样一个极端的情况 比较极端,但可能不会出现 我们想象一下这个极端的情况 电话不接,微信不回,邮件不回 那接下来你会怎么办呢?

这种极端的情况确实没有相关注意经验 这个东西特别的那个 比如说是一个客户的一个问题 或者说是一个供应商 是问题的重要性吗 就是可能如果说非常重要 可能会电话邮件交流都不交流的话 就是最直接的方法找人 冲到他办公室找到他是吧 如果不是咱这边也可以去 或者去别的地方 就形容说是这样去找人家然后来处理一下 就是我不喜欢把问题 就是就不管是和女朋友吵架 或者说是实验室的一些问题没有解决 我不喜欢把问题拖着 就是一拖就很难受 就是如果这个人不理你 就是他不理你你也很难受 所以我比较喜欢直接的方式 有可能不理我我就去找你嘛 也不知道对不对的一个简单的解决方法 你会不会觉得这个人在针对你 或者说你们在交流的时候 当对方的态度也不是很好 冷嘲暗讽 或者挖苦你 当着很多人的面 你会陷入这种情绪化吗 如果说你会陷入一种情绪化的话 你是当场的话 你会怎么面对这个问题 或者说事后 你会怎么回想这个事情 被挖苦,可能我们之前在工作出现问题或者说是交流的过程中也会有这种情况 但是我一般不会轻轻地去跟他辩论争吵或者直接当面争吵 可能在正常的工作过程中可能还会比较平静地去交流 就是我们那边一个技术的 可能嘲讽嘛 或者就是 就是暗讽 就是你做实验的你懂什么呀 就是这种 就是你就是一个做实验的嘛 或者说就是 我就非要让你做 你就得给我做 你就是做实验的嘛 这是我的语气 就你人一听了你就 对吧 你说他说的有错吧 你也不能说他有错 但是就是很不爽 就是这种高再上 因为你们是平级 那种感觉 就很不爽的感觉 然后当时他就非要跟我们做实验 或者说怎么样 然后我当时也没跟他吵 因为一方面吵了也没有用 可能就是这样 然后我当时就跟他说 我们做实验就是做实验 你有要求我们也确实可以满足 但是第一个要求 你不能说是因为你一眼 就是你给我们一些东西 你得有一些输入 上下级不合规模,他就是想要弄,但是我们那边也没有上下级要求严密那样给你签字,他可能自己找一下也行,有时候就他弄 但是你那样能弄还弄我们,你都没有优称,然后你还那样说 所以可能就当时不会挺乱,但是事后我会跟他找他,会在单独谈一谈 当时事后找他之后 他说是因为领导一直催着他要个结果 可能一时的话不太好 就是说可能 不管他怎样说吧 我可能也 当然事情确实很不爽 但是我会勤快的去动作的解决 就可能会事后再 跟他读图交流 我来再把这个再给你往前推一步 假如说这个人他就是有意无意的 故意要惹恼你 故意要让你下不了台 如果你没有当场反驳 你会晚上睡不着觉吗?

或者说你会晚上反复地在回想这个事情吗?

冲到台上,时候冲过去 对,或者晚上想,心里想着 当时我要是这么说,这么说就好了 会这样吗?

会这样想,就是可能比如觉得 没吵好,会这样 但是不会说睡不着觉这样 可能没吵好,记一下 又想不起来我之前总结的是啥了 那个时候就这个时候只能等事后你才会回想起来 这个时候人会憋伤的多了 此情可待 成岁矣 对 所以年轻人有些事情的话 不需要那过于的收敛 所以也是我们性格测试 我之所以问这个问题的一个原因 说你是这个安逸人 稍微往内寻找 该往外的稍微往外一点 最后我还是要给你再简单的介绍一下整个实验室的基本情况,组织架构等等 首先我们是一个跨国公司,我们这边是一个宁波分公司 是一个独立的,我们是美资企业,总部在美国的田纳西州诺克斯威尔市 我们这边有研发团队,同样的在总部也有一个研发团队 我们这边的话是宁波研发项链的工程样件实验室 我们的实验室跟一般的实验室稍微有点不同的是 我们同时兼顾着样件生产的工作 所以严格来说我们可以算是一个生产车间加一个实验室 而且生产的一个活动占我们整个团队的工作的比例的话 所以我们这边是一个独立的部门,我们并不是隶属于哪一个部门的,我们整个宁波研发部有两个部门,一个是我们实验室,一个是陈明工程师,都隶属于我们刘总这边下面的。 所以我们的整个生产是参考整个生产流水线,仿制它做的一个手工样件生产 所有的工艺是一模一样的,但是没有那么自动化,集成程度没那么高 但是我们也有严格按照那个工艺流程来做质量管控做安全防护 然后一步一步的使用我们的产品成型 这样的在这个过程中产品成型的过程中涉及到一些测试环节 要搜集他的数据 然后成品的话做完了之后呢会有一些大型的比较复杂的测试项目 涉及到性能方面的 会有一些性能的功能的疲劳的强度的等等这些都在里面 这个等一下逛这个车间的时候可以给你介绍 实验室内部的划分的话 我们目前是主要是划分了三块 可以说是两大块 一个是生产团队 一个是测试团队 生产团队的话 细分下来是 缤状产品生产和柱状产品生产 这样的 等我们这边入职的话 我们主要是负责了我们整个生产和测试的产品 还有测试环节的一些可能性 我们的生产和验证我们的阶地里面 我这里就不过多的展开了 大概这么个情况 好吧 这里面的话 从现在开始 假如说有幸你入职了 现在就要跟你交代的一点就是 是它给汽车里面的安全气囊充气的那个工件 它是一个压力容器 它的内部的那个压力的话呢都是几千PSI的 我转化成兆帕都是100兆帕以上 所以说在生产过程中的话呢 一定要特别注重安全 因为它里面还涉及了一些含能的材料 比如说我们的产气药啊 传报药啊 还有一些电火管 当然这是我们的行业 我们行业里面的一些中的名词 但你就可以把它想象成我们小时候放鞭炮里面的一些火药 一燃 然后不一定一爆 但是因为它会迅速的膨胀在科体里面 那就像手雷一样会有一定的危险 包括一些生产的设备 所以你要先保护好自己 然后的话呢 现场的5S安全一定要特别特别特别小心 好吧 要从现在起 要时刻记在脑子里面 OK 时刻谨记着一点 好吧 嗯好 行 我可以 我没什么这样的问题 行 嗯没问题 那就这样 那要么你先出去等我一下 我们几个简单的讨论一下好吧 好 等一下然后我们再去逛一下车间 我先说一下我没什么太大问题 跟第一次面试的冲刻度印象得分 总体的感觉冲刻度还是比较一致的 没什么毛病 我觉得可以 总的来讲是这样的 我感觉性格测试还挺重的 是哦,新的测试有详细的细节吗?

我看一下 我把文字发给你 哦,你是电子版的 对,有个链接你也可以测一下 要不然我把链接也拿给你测一下 我也测了 我也测了 我也是阿云人 你自己也是阿云人 对 那个,那个叫啥 我有一点难题 表示是今天他回答你的问题 几乎他都没有直接回答 基本上都是要花很长的时间 这个跟第一次面试的情况有个比较大的直观的感觉 第一次回答问题我觉得他基本上是比较直接的 而且回答的我也个人认为是没啥问题 今天回答你的大部分的问题都不是非常直接的 但是就是绕绕绕 尽管有提醒还是绕绕绕 但是还是绕不回来 这个有点不太跟我的上次的印象一致 但是我觉得不知道是不是今天有点稍微紧张 但是语气还是比较沉沦的 这个我是有一个感觉 就是说你的意思是他没有get到我的问题是吧 对,可能get到了,但是也不知道怎么回答 就有点绕 毕竟现场面试 上次的问题可能很多有一部分是集中在他日常的工作 所以他可能有点带入的比较快 有几个点,本来询问一两个问题,我没有意识到,看见后面未来又问到问题,接着突然意识到这个点。 那么就是,当然这个呢,就是说,存在一个点就是他接受一个新任务的时候呢,是需要给他,可能前期的话呢,是需要讨论一下。 包括他理解的就是说领导要有清晰的一个沟通,就是要求要提的清清楚楚,让他有一些不太理解的地方也可以在双向的沟通里澄清, 然后便于他往下去做的时候没有丢掉你的目标,这个可能提醒了我一点,但是也许他有点紧张,今天一现场稍微有点紧张,所以的话可能会有一点分别。 另外一个呢,女朋友确实谈恋爱的话,如果她女朋友在江泽地方找啊,那会是未来的一个好的点。 所谓好的点是说,如果女朋友在托普,比如后面经过一两年,然后在托普能够稳定下来,至少是她会在这个地方比较稳定。 如果在一两年之内也没有达成这个情况,就要看感情的老古程度了。 这个是个变数。 这个方面我觉得他应该没啥,男生一般是影响比较少。 我理解,但就是说你再,即使过来了,可能关于这方面的话,也收拾了关系一下。 你得关心他一下,为啥呢?就是说你得了解一下他们的这个进化。 当然我们是假设人家的感情是比较好的,对不对?

那到时候说不定还得要Justin帮忙,对吧?

Torpe这边是不是有机会呢?

虽然他说他只是在那一年,但是一般新员工的话,跳槽最好的就是两到三年吧,对不对?

这个就大约变成两到三年之后,熟悉的行业跳槽是最好跳的。 是的。 最好跳的,所以这个时候,到时候你平时了解完之后,到时候看看有什么需要我们Justin帮忙的,好不好?

但這個東西我們就是提前考慮到這個點 嗯 嗯 嗯 嗯 那另外的一個 但我覺得不是什麼問題啦 因為對產品不了解 對流程不了解 嗯 他還是蠻自信的 就是說 能夠給我們團隊帶來一些新的一些不一樣的地方 啊 那這個正好你們的團隊也是比較沉悶 呵呵 你們團隊呢這個當然是跟我也有關係啊 其實我也比較沉悶啊 就是我可能在工作上說的比較多 其实在不工作的时候很少说话 我很少说话 所以的话可能跟我们整个环境也有关系 你的呢小贝也不爱说 松树也是 松树有孩子了 对也是比较沉嫩的 那硕硕相对来讲还活肥一点 然后怀民也不怎么爱说 所以的话如果他能够来调节一下你们的氛围 活跃一下你们的气氛团队氛围 哇,因为他也觉得他蛮自信的,一看怎么用,就说是个人来的 我觉得他是那种对熟人,或者说处的还好啦,可能会显得大大咧咧的 但是可能大部分的时候还是偏内向一些的 他的性格应该是不会主动去接触熟透陌生人的,但是对于熟悉的人会放得比较开 这就是我们的爱人 就是扛压,稳定,比较有耐心,温暖的听者 缺点也不过deep-seeker了一下,没有计划 没有什么计划?

没有计划,这个从他rocking down他3到5年规划 也许是他紧张,也许他确实没有想过这个东西 不是紧张,他就是那样的 我跟他说了,其实不是他就是那样的 他可能以后很长的规划 然后他如果一旦适应了,可能这块你也不用太操心了,可能会管理比较好的那种 另外一个我要说的是,他穿T恤过来的,这个你们有什么,但是可能年轻人面试的时候都穿T恤,现在你过来也穿着带动动的东西,你有点 没有,我上面穿的是西装 哦,你上面穿的是西装,下面穿的,这个非常不正式,我觉得现在年轻人都是,你是觉得不够正式是吧 这个主要是看年轻人嘛 我面试的时候永远都是西装历代的 但我们这个年代是我们这个年代 就是你们这个年代不一样 因为他又是你和我的体格 我都没有强调过要做什么 我还好,我不是很看重这一些 但是像我以前我找工作 我会稍微在我的衣柜里面找个稍微正式一些的 一般是哪个 我的理解是一般小哥他可能会穿个衬衣过来 有可能 OK 所以我去问一下你们的 因为你们是 哥们有好几个代勾的 所以我问问你们的 你们觉得没啥 我觉得这是个正常情况 那我以为OK 那像之前他们 同好李明煌来面的时候 是什么样的 李明煌好像比较正式一点 李明煌是视频 是比较正式 同好 同好还好 同好也是个年轻人 我面试过 包括我见到的 大部分都是比较休闲的 所以要看时代的变化 我不强求 我面试的时候是拎着个头盔进来的 哦 是啊 你是骑摩托的嘛 对 我就希望穿得齐心不力的头盔进来 所以你们那儿也就喜欢你了 现在年轻人不一样 人们想的不一样 但是事情做好 有自己的个性 自己的想法就好了 嗯 OK 所以你是担心他不重视这个面试是吗 对 我觉得还好,他比较重视 他飞机机票也是发给我,几点的航班,什么时候起飞也跟我说过 他把他的时间点是一会儿都告诉我的 到这边来 那你待会还要带他去宿舍吗?

宿舍我一会看一看吧,我有空就带他去吧 咱们宿舍我很怕宿舍看了人家跑 那就算了 它在哪儿就行了 刚才在这片 这几号楼都是 在几号楼 你说我们工程师 主要是在这儿的都是哪儿 他也不用挑挑选选的就好了 是的 他也没房间了现在 行 那就先这样子 行 好 那我带大家去逛一下 还有一个大家注意一下 就是说 有什么睡前的疑问吧 这不合适吧 他是不是有点紧张啊 嗯这个经常有人问 因为这张表后面写的是year 他没有写全世界 ok ok 那我先打开上面去解释啊 就我觉得你你带他 就是我们后面面试的时候有一点 过于 清晰的表达了我的想法 但是我的意思是你带他看的时候 不用表述这个意思,只是双方的相互了解 因为最终的通知是要由Justin来做 那我们这段时间就暂时不招募了 我是这么想的,不用了,我们本来团队就精力有限 除非他在某一个时间拒了我们,说我觉得不好 我們就再實現獨面 好不好 可以 好 行 那就 我們去花園區吧 花園區 OK 行 好 我先不去了 我先要去花園區
