### Ad-04_Human Resources — 组织与整理方案（初版）

目标：在不破坏现有证据链与历史存档的前提下，建立“可控访问、易检索、可审计、可交付”的人事与绩效资料库结构。

---

### 一、信息分级与权限
- **Level A（严格保密）**：身份证/护照/签证、调薪明细、绩效考评单、员工通讯录（含联系方式）、面试录音、发票/票据影像
  - 存储：加密卷或受控网盘（建议：`Encrypted/HR-Sensitive/`）
  - 访问：仅HR负责人与授权管理层
  - 外发：统一脱敏，带水印与外发登记
- **Level B（内部保密）**：组织架构、岗位职责、奖金方案、培训资料
- **Level C（内部共享）**：公开模板、表单空白版、通用指南

---

### 二、目录重构建议
在现有根目录下新增两个顶级区：
- `Sensitive/`（Level A）：迁入下列目录文件的“受控副本/归档版”
  - `04 - 调薪明细/`（整目录）
  - `03 - EDGE KPI - Performance Evaluation/` 中个人绩效表与述职PPT（按年份+员工子目录）
  - `06 - 费用报销/` 中证照、票据影像与报销明细
  - `08 - 人事表单/` 中含个人信息的实例（保留模板在公共区）
  - `员工通讯录*`、护照/签证/EVUS图片与PDF
- `Templates/`（Level C）：放置空白模板与公共指南
  - `08 - 人事表单/旧版表单/` 中选取“最新空白模板”整理重命名
  - `05 - 工厂绩效方案/` 中正式发布的政策/空表单
  - `03 - EDGE KPI` 中“模板化”目标设定与评审流程文档

原目录保留，但将“实例/历史数据”与“模板/已发布政策”分离，建立指向链接或索引清单，避免误用。

---

### 三、命名与版本规范
- 统一命名：`部门-主题-人名-YYYYMMDD[-vN].扩展名`
  - 例：`NEL-PerformanceReview-RockyShi-20250130-v2.xlsx`
- 最终版指针：同目录保留 `LATEST -> 实际文件` 的“别名说明.txt”或快捷方式
- 组织架构：保留 `Official-YYYYMMDD.pdf` 与 `Working-YYYYMMDD.pptx` 双轨
- 考勤表：去除 `[3]` 等歧义标识；保留 `来源/生成方式` 备注

---

### 四、索引与检索
- 在根目录维护索引表：`INDEX.xlsx`
  - Sheet1：员工 × 年份 × 绩效文档链接
  - Sheet2：调薪记录索引（日期/人/幅度/附件）
  - Sheet3：差旅与报销索引（项目/时间/金额/附件）
  - Sheet4：模板清单（版本/适用范围/维护人）
- 每个大目录放置 `README.md`：说明结构、权限、联系人、变更记录

---

### 五、保留与清理
- `teams-transcription-extension/`：迁出至 `Projects/Teams-CC-Extractor/`（单独仓库或独立项目区），根目录仅保留一份明确日期的 `backup.zip`
- `O-chart` 多版本：保留 `Official-20250428.pdf` 与近三版工作稿；其余入 `Archive/OrgChart/`
- `考勤` 重复件：核对差异，保留来源明确者；添加 `README` 说明生成/来源

---

### 六、安全与合规
- 存储加密：磁盘加密（FileVault）+ 加密压缩包（Sensitive导出场景）
- 外发脱敏：通讯录、薪酬、绩效、证照等外发前统一脱敏模板
- 水印与审计：对外版本添加水印；建立外发登记表（日期/接收人/用途）

---

### 七、自动化与日常运维
- 周任务：
  - 新增文件分拣（敏感/非敏感）与索引更新（15分钟）
  - 版本归档：对“Working”稿每周合并为一版
- 月任务：
  - KPI/绩效资料月度回收与索引更新
  - 目录健康检查（重复/大文件/外发记录核对）
- 工具建议：
  - 去重：`fdupes`/`rdfind` 或 macOS 智能文件夹
  - 标签：Finder 标签与评论；Hazel 自动分拣（命名/标签规则）

---

### 八、立即执行清单（本周内）
1) 迁移 `teams-transcription-extension` 至 `Projects/`，根目录仅保留 `Teams-CC-Extractor-YYYYMMDD.zip`
2) 在根目录建立 `Sensitive/` 与 `Templates/` 两个新顶级区
3) 将 `04 - 调薪明细/` 与证照影像先行复制到 `Sensitive/`（保留原路径，先做受控副本）
4) 为 `03 - EDGE KPI` 建立 `INDEX.xlsx` 初版（人名×年份×文件链接）
5) 在 `01 - JD和组织架构图/岗位职责/` 与 `08 - 人事表单/` 建立 `README.md`，标注最新模板位置与维护人

---

如需，我可以继续：
- 批量建立上述目录与`README.md`骨架
- 生成 `INDEX.xlsx` 初版并自动扫描填充可见文件链接
- 执行去重检测与清理建议清单


