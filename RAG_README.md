### 轻量RAG索引使用说明（本地文档问答）

本目录已生成 `RAG_INDEX.jsonl`，用于对 `Ad-04_Human Resources/` 下关键文本与技术说明进行“可检索增强生成（RAG）”的快速问答与引用。

---

### 文件说明
- `RAG_INDEX.jsonl`：逐行JSON，字段：
  - `id`：唯一标识
  - `title`：条目标题
  - `source_path`：源文件相对路径
  - `tags`：主题标签数组
  - `content`：精炼摘要（便于语义检索与上下文拼接）
  - `created_at`：索引生成时间

---

### 适用范围
- 管理与HR方法论：`NEL价值贡献*`、沟通策略、培训方案
- Teams转录扩展：README/安装/指南/调试/隐私/架构/测试与阶段总结
- 测试脚本：连接修复、相似度阈值、防抖、根因复现等

---

### 如何用于问答（示例）
1) 意图 → 关键词/标签：
   - 例：想问“如何在月度报告中凸显效率提升指标？”
   - 关键词：`NEL`、`Dashboard`、`Efficiency`、`Template`

2) 检索 → 过滤命中条目：
   - 命中 `nel-dashboard-template`、`nel-system-summary`、`nel-training-plan`

3) 组装上下文 → 生成回答：
   - 将命中条目的 `content` 作为参考上下文，附 `source_path` 作为可追溯来源

---

### 在本地脚本中使用（Node.js示例）
```js
import fs from 'fs';

const indexPath = './RAG_INDEX.jsonl';
const index = fs.readFileSync(indexPath, 'utf8')
  .trim()
  .split('\n')
  .map(line => JSON.parse(line));

function semanticFilter(q) {
  const query = q.toLowerCase();
  return index
    .map(e => ({
      e,
      score: [e.title, e.tags.join(' '), e.content]
        .join(' ').toLowerCase()
        .split(query).length - 1
    }))
    .filter(r => r.score > 0)
    .sort((a,b) => b.score - a.score)
    .map(r => r.e);
}

const results = semanticFilter('NEL 仪表盘 效率 提升');
console.log(results.slice(0,3));
```

---

### 提问建议
- 明确对象：如“在 `NEL价值贡献月度仪表盘` 中如何下钻效率？”
- 明确产出：如“给出管理层可用的2段执行摘要 + 指标公式建议”
- 明确约束：如“仅引用索引中的文档，输出附来源文件名”

---

### 索引扩展（可选）
- 增量纳入：`03 - EDGE KPI` 细化到“人×年×表单”；`04 - 调薪明细` 仅存元数据（避免敏感内容）
- 切分粒度：对长文拆分为段落级条目（段id/段落起止）
- 语义向量：后续可用 `openai text-embedding-3-large` 生成 `embedding` 字段（单独存embeddings文件）

---

如需，我可以：
- 扫描更多目录生成扩展索引（含隐私分级）
- 生成带 `embedding` 的向量检索索引与本地查询脚本
- 为你的常见问题建立预设查询与回答模板


