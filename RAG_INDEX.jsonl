{"id":"nel-dashboard-template","title":"NEL价值贡献月度仪表盘 Excel模板设计","source_path":"NEL价值贡献月度仪表盘模板设计.md","tags":["NEL","Dashboard","Excel","KPI","Template"],"content":"Excel模板4大Sheet：1) 仪表盘总览：运营健康度、效率提升、风险/质量、技术创新与能力建设，含趋势与自动摘要；2) 工作日志：日期/人/类别/内容/设备/耗时/产出，7类工作下拉；3) 月度价值分析：时间分布、关键指标趋势、亮点项目筛选；4) 亮点项目报告模板。含SUMIF/COUNTIF/VLOOKUP、条件格式、趋势图标与下拉校验。使用：每日记录→周检查→月汇总→项目包装。","created_at":"2025-07-28"}
{"id":"nel-training-plan","title":"NEL价值贡献仪表盘系统 - 团队培训实施方案","source_path":"NEL团队培训实施方案.md","tags":["Training","NEL","Process","Work Categories"],"content":"2小时启动会：背景目标/模板演示/7类工作详解/实操练习/流程确定。标准化：分类决策树与边界处理、价值量化指导（效率、预防、质量、创新、能力）。习惯建立：密集指导→自主记录→复盘。月度复盘1.5小时：数据回顾、亮点分享、改进讨论、成果输出。预期：救火占比下降，创新与效率项上升，绩效评估有据。","created_at":"2025-07-28"}
{"id":"upward-communication","title":"NEL团队向上管理沟通优化策略","source_path":"向上管理沟通策略方案.md","tags":["Communication","Management","Jim","Briefing"],"content":"以仪表盘数据为核心：双周简报（固定模板）+ 月度战略对话（议程：数据回顾/亮点深入/挑战/规划）。\n'闪光点'标准：行业领先、显著影响、创新突破、外部认可；包装为商业价值语言。\n影响力扩展：向更高层级展示、跨部门项目、外部交流、客户反馈；成功共享机制。\n语言风格：数据驱动、问题-方案-成果、长期视角、风险意识。指标：沟通频率、反馈质量、决策参与、资源获得。","created_at":"2025-07-28"}
{"id":"nel-system-summary","title":"NEL价值贡献月度仪表盘系统 - 实施总结","source_path":"NEL价值贡献系统实施总结.md","tags":["Summary","NEL","Dashboard","Implementation"],"content":"三组件：Excel模板、团队培训流程、向上管理策略。技术特点：自动汇总、价值导向分类、可视化、项目化包装。成功因素：领导推动、团队参与、持续优化、价值展示。预期：1-3个月建立价值思维与首批亮点；3-6个月救火下降、创新提升、话语权提升；6-12个月标杆团队与影响力扩大。风险：实施阻力、记录质量、分类混乱、沟通过度，配套应对。","created_at":"2025-07-28"}
{"id":"teams-readme","title":"Teams Meeting Transcriber - README","source_path":"teams-transcription-extension/README.md","tags":["Teams","Chrome Extension","Transcription","Architecture"],"content":"特性：实时转录（麦克风/标签页/桌面音频）、多语言、自动保存、TXT/JSON/SRT/VTT导出、会议检测、隐私本地处理。架构：background/content/audio-capture/speech-recognition/popup。使用：加入会议→启动转录→实时查看→导出。权限说明与隐私本地存储。","created_at":"2025-07-28"}
{"id":"teams-install","title":"Installation Guide","source_path":"teams-transcription-extension/INSTALLATION.md","tags":["Install","Chrome","Icons","Developer Mode"],"content":"前置：Chrome88+、麦克风、网络、Teams。开发者模式加载：准备文件与图标→chrome://extensions→Load unpacked→Pin。可选企业部署与卸载清理。常见安装问题：图标缺失、权限/版本、文件权限等。","created_at":"2025-07-28"}
{"id":"teams-cc-guide","title":"Teams CC转录使用指南","source_path":"teams-transcription-extension/TEAMS_CC_GUIDE.md","tags":["Teams CC","HowTo","CN"],"content":"快速开始：开启Teams实时字幕（中文）→扩展选择“Teams字幕提取”→开始转录。两模式对比：Teams字幕提取（质量高、无需麦克风）vs 独立语音识别（更自主）。常见问题：未开启CC、内容为空、质量差的排查步骤。","created_at":"2025-07-28"}
{"id":"fixes-summary","title":"Fixes Summary","source_path":"teams-transcription-extension/FIXES-SUMMARY.md","tags":["Bug Fix","Speaker Detection","UI CSS","Display Logic","Popup"],"content":"修复：1) 说话人识别（改进getCurrentUserName/选择器/回退）2) UI样式冲突（移除内联冲突、样式隔离、响应式）3) 转录显示/Latest摘要 4) 右上角对话框错误状态与反馈。含测试核对项与关键代码片段示例。","created_at":"2025-07-28"}
{"id":"debug-summary","title":"调试总结报告（isSupported修复）","source_path":"teams-transcription-extension/DEBUG_SUMMARY.md","tags":["Debug","isSupported","Diagnostics"],"content":"核心错误：this.isSupported is not a function。根因：类加载顺序与静态方法误用。修复：调用安全化、加载时机延迟、全局错误捕获、自动诊断脚本、改进报错信息。验证：类加载、静态方法、实例、API、页面检测、错误处理全通过。提供部署与用户测试步骤。","created_at":"2025-07-28"}
{"id":"decision-matrix","title":"Strategic Decision Matrix: Fix vs. Refactor","source_path":"teams-transcription-extension/DECISION_MATRIX.md","tags":["Strategy","Refactor","Risk","Cost-Benefit"],"content":"评估修修补补 vs 架构重构：战术修复短期见效但技术债激增，概率低；战略重构3周，一次性解决根因，长期可持续。量化对比、风险矩阵、最佳实践对齐、成功标准与回滚计划，结论：建议重构。","created_at":"2025-07-28"}
{"id":"error-analysis-guide","title":"Chrome错误日志分析指南","source_path":"teams-transcription-extension/ERROR_ANALYSIS_GUIDE.md","tags":["Error Analysis","Console","Extension"],"content":"获取日志方法：加载debug.js或在扩展/背景页/内容页查看错误。关键错误模式：isSupported误用、类未定义（脚本顺序）、未初始化实例。提供定位技巧、解决方案示例与错误报告模板、特定调试命令与实时监控。","created_at":"2025-07-28"}
{"id":"troubleshooting","title":"Troubleshooting Guide","source_path":"teams-transcription-extension/TROUBLESHOOTING.md","tags":["Troubleshooting","HowTo","Permissions"],"content":"快速诊断清单（版本、启用、页面、麦克风、网络、权限）。常见问题：无法启动、无音频、质量差、扩展未加载、面板异常、保存导出问题；分别给出权限/设置/浏览器与系统层面的解决路径。含浏览器/OS特定问题与紧急兜底方案。","created_at":"2025-07-28"}
{"id":"privacy-policy","title":"Privacy Policy","source_path":"teams-transcription-extension/PRIVACY.md","tags":["Privacy","Local Storage","Permissions"],"content":"原则：本地处理优先、最小化收集、用户可控、透明开源。收集：转录文本/时间戳/基本说话人/URL与设置；不收集：个人信息、原始音频、会议内容、追踪。存储：本地、加密、保留50条。权限说明与第三方（Web Speech API）说明及合规。","created_at":"2025-07-28"}
{"id":"privacy-statement","title":"隐私声明（中英）","source_path":"teams-transcription-extension/PRIVACY_STATEMENT.md","tags":["Privacy","CN","Web Speech API"],"content":"中文/英文版：Web Speech API可能使用浏览器供应商云端；本地数据存储可清除；Teams CC模式仅读DOM字幕。权限用途：麦克风/标签页/桌面捕获/存储。数据控制：模式选择、删除记录、禁用自动保存等。","created_at":"2025-07-28"}
{"id":"strategic-analysis","title":"Strategic Meta-Analysis","source_path":"teams-transcription-extension/STRATEGIC_ANALYSIS.md","tags":["Strategy","Meta","Refactor"],"content":"识别连锁故障模式：通讯脆弱、假设驱动、被动式错误处理、Manifest V3迁移缺口。提出系统性评估、测试金字塔、现代通信（Popup→Service Worker→Content）与初始化协议、成功指标与风险缓释。结论：转向战略性重构。","created_at":"2025-07-28"}
{"id":"single-ui-guide","title":"Single-Interface UI Architecture Guide","source_path":"teams-transcription-extension/SINGLE_INTERFACE_UI_GUIDE.md","tags":["UI","Feature Flag","Panel"],"content":"单界面UI：Popup变为状态页，面板为主交互（自动最小化出现、位置持久化、拖拽、快捷键、特性开关可回滚）。保持健壮系统：状态机、错误管理、资源管理、数据完整性与会议检测。提供启用方法与测试清单。","created_at":"2025-07-28"}
{"id":"transcription-methods","title":"转录方案对比","source_path":"teams-transcription-extension/TRANSCRIPTION_METHODS.md","tags":["Transcription","Design Choices"],"content":"方案A独立识别：独立/通用/可控，但需麦克风/依赖音质与Web Speech API。方案B提取Teams CC：质量高/无需麦克风/多人识别，但依赖开启CC与语言支持。推荐优先A，特定情况下用B。","created_at":"2025-07-28"}
{"id":"test-setup-guide","title":"Test Setup Guide","source_path":"teams-transcription-extension/TEST_SETUP_GUIDE.md","tags":["Testing","Playwright","Jest"],"content":"前置：Node16+、Chrome/Chromium。结构：unit/integration/e2e。运行：npm run test:unit / :integration / :e2e / :all；Playwright安装与报告位置；编写测试约定与故障排除。","created_at":"2025-07-28"}
{"id":"final-test-instructions","title":"Final Test Instructions","source_path":"teams-transcription-extension/FINAL_TEST_INSTRUCTIONS.md","tags":["Testing","Diagnostics","HowTo"],"content":"步骤：刷新扩展→加入Teams→在控制台加载run-all-diagnostics.js→查看状态FULLY_WORKING/NEEDS_ATTENTION。分别测试Teams CC与语音识别两模式、检查无错误、反复启动/切换/刷新。提供诊断报告结构与失败处理。","created_at":"2025-07-28"}
{"id":"phase1-completion","title":"PHASE 1 COMPLETION SUMMARY","source_path":"teams-transcription-extension/PHASE1_COMPLETION_SUMMARY.md","tags":["Refactor","Service Worker","Phase 1"],"content":"已完成：现代Service Worker路由与状态管理、content-modern/popup-modern、测试框架与验证套件、Manifest V3更新。达成指标：通信可靠、初始化协议、错误恢复、测试覆盖基础。下一步Phase2：通信层重建、生命周期管理、持久化与集中化错误处理。","created_at":"2025-07-28"}
{"id":"conn-fix-test","title":"Connection Fix Validation Test","source_path":"connection-fix-test.js","tags":["Tests","Connection","Retry","Injection"],"content":"测试增强连接稳定性：重试机制（最大重试、指数退避、首次失败注入内容脚本）、消息处理健壮性（初始化前排队/错误响应）、初始化恢复（捕获/延迟/二次尝试/日志）。输出报告与期望结果。","created_at":"2025-07-28"}
{"id":"solution-validation-test","title":"Solution Validation Test Suite","source_path":"solution-validation-test.js","tags":["Validation","Thresholds","Debounce"],"content":"验证三大方案：1) 消除双重处理（content仅精确匹配安全网）；2) 自适应相似度阈值（中文/标点/空格差异阈值动态）；3) 智能防抖（保留内容变化，过滤格式变化）。包含相似度计算与验证报告。","created_at":"2025-07-28"}
{"id":"root-cause-tests","title":"Root Cause Test Suite","source_path":"root-cause-test-suite.js","tags":["Root Cause","Threshold Boundary","Race Condition","Chinese Handling","Timing"],"content":"系统性复现：双重处理架构、阈值边界（0.88附近）、防抖竞态导致丢失更新、中文文本处理（空格/标点/姓名空格）、时间窗口边界。输出各类问题统计与报告。","created_at":"2025-07-28"}

